{"$schema": "../cabinet_schema.json", "required_variables": [], "variables": {}, "include": [{"name": "szafa", "file_path": "./szafa.json", "rotation": "NONE", "reference_location": {"x": 0, "y": 0, "z": 0}, "variables": {}}, {"name": "kaloryfer", "file_path": "./nad_kaloryferem.json", "rotation": "ROTATE_CLOCKWISE_90", "reference_location": {"x": "770+18+3", "y": -18, "z": "1530+3"}, "variables": {}}]}