{".class": "MypyFile", "_fullname": "board_cad", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BandingType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.BandingType", "name": "BandingType", "type": {".class": "AnyType", "missing_import_name": "board_cad.BandingType", "source_any": null, "type_of_any": 3}}}, "BoardCad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board_cad.BoardCad", "name": "BoardCad", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board_cad.BoardCad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "board_cad", "mro": ["board_cad.BoardCad", "builtins.object"], "names": {".class": "SymbolTable", "__doc_object": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "board_cad.BoardCad.__doc_object", "name": "__doc_object", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "located_board"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_cad.BoardCad.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "located_board"], "arg_types": ["board_cad.BoardCad", {".class": "AnyType", "missing_import_name": "board_cad.LocatedBoard", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoardCad", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__located_board": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "board_cad.BoardCad.__located_board", "name": "__located_board", "type": {".class": "AnyType", "missing_import_name": "board_cad.LocatedBoard", "source_any": null, "type_of_any": 3}}}, "get_document_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_cad.BoardCad.get_document_object", "name": "get_document_object", "type": null}}, "instantiate_in_freecad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_cad.BoardCad.instantiate_in_freecad", "name": "instantiate_in_freecad", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board_cad.BoardCad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board_cad.BoardCad", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dimension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Dimension", "name": "Dimension", "type": {".class": "AnyType", "missing_import_name": "board_cad.Dimension", "source_any": null, "type_of_any": 3}}}, "FreeCAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.FreeCAD", "name": "FreeCAD", "type": {".class": "AnyType", "missing_import_name": "board_cad.FreeCAD", "source_any": null, "type_of_any": 3}}}, "LocatedBoard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.LocatedBoard", "name": "LocatedBoard", "type": {".class": "AnyType", "missing_import_name": "board_cad.LocatedBoard", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Part": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Part", "name": "Part", "type": {".class": "AnyType", "missing_import_name": "board_cad.Part", "source_any": null, "type_of_any": 3}}}, "Point": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Point", "name": "Point", "type": {".class": "AnyType", "missing_import_name": "board_cad.Point", "source_any": null, "type_of_any": 3}}}, "Side": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Side", "name": "Side", "type": {".class": "AnyType", "missing_import_name": "board_cad.Side", "source_any": null, "type_of_any": 3}}}, "Surface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Surface", "name": "Surface", "type": {".class": "AnyType", "missing_import_name": "board_cad.Surface", "source_any": null, "type_of_any": 3}}}, "Vector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "board_cad.Vector", "name": "Vector", "type": {".class": "AnyType", "missing_import_name": "board_cad.Vector", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_cad.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "from_face": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["face"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_cad.from_face", "name": "from_face", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["face"], "arg_types": [{".class": "AnyType", "missing_import_name": "board_cad.Part", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_face", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "board_cad.Surface", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_color_for_banding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["banding_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_cad.get_color_for_banding", "name": "get_color_for_banding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["banding_type"], "arg_types": [{".class": "AnyType", "missing_import_name": "board_cad.BandingType", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_color_for_banding", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\board_cad.py"}