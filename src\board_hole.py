from abc import ABC
from dataclasses import dataclass


@dataclass
class Hole(ABC):
    """Represents a hole in a board."""
    diameter: int
    depth: int


@dataclass
class FaceHole(Hole, ABC):
    position_from_left: int
    position_from_bottom: int

    def __post_init__(self):
        if self.diameter not in [3, 5, 8, 10, 15, 20, 35]:
            raise ValueError("Invalid face hole diameter " + str(self.diameter) +
                             "mm, allowed values: 3, 5, 8, 10, 15, 20, 35mm")
        if self.depth not in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, -1]:
            raise ValueError("Invalid face hole depth " + str(self.depth) +
                             "mm, allowed values: 2-15mm and -1 as through hole")


@dataclass
class BackHole(FaceHole):
    pass


@dataclass
class FrontHole(FaceHole):
    pass


@dataclass
class SideHole(Hole, ABC):
    def __post_init__(self):
        if self.diameter not in [4, 8]:
            raise ValueError("Invalid side hole diameter " + str(self.diameter) + "mm, allowed values: 4, 8mm")
        if self.depth < 2 or self.depth > 35:
            raise ValueError("Invalid side hole depth " + str(self.depth) + "mm, allowed values: 2-35mm")


@dataclass
class LeftRightHole(SideHole, ABC):
    position_from_bottom: int


@dataclass
class LeftHole(LeftRightHole):
    pass


@dataclass
class RightHole(LeftRightHole):
    pass


@dataclass
class TopBottomHole(SideHole, ABC):
    position_from_left: int


@dataclass
class TopHole(TopBottomHole):
    pass


@dataclass
class BottomHole(TopBottomHole):
    pass
