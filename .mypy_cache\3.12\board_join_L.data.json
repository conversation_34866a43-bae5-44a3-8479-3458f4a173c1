{".class": "MypyFile", "_fullname": "board_join_L", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BoardJoin_L": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board_join_<PERSON>.<PERSON>n_L", "name": "BoardJoin_L", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_L", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "board_join_L", "mro": ["board_join_<PERSON>.<PERSON>n_L", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "board_1", "board_2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_<PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "board_1", "board_2"], "arg_types": ["board_join_<PERSON>.<PERSON>n_L", "builtins.str", "board.LocatedBoard", "board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoardJoin_L", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_dowel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "location", "dowel_length", "dowel_diameter", "iterator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_<PERSON>.add_dowel", "name": "add_dowel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "location", "dowel_length", "dowel_diameter", "iterator"], "arg_types": ["board_join_<PERSON>.<PERSON>n_L", "base.Point", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_dowel of BoardJoin_L", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "board_edge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.board_edge", "name": "board_edge", "type": "board.LocatedBoard"}}, "board_edge_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.board_edge_side", "name": "board_edge_side", "type": "base.Side"}}, "board_face": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.board_face", "name": "board_face", "type": "board.LocatedBoard"}}, "board_face_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.board_face_side", "name": "board_face_side", "type": "base.Side"}}, "calculate_even_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "edge_spacing", "min_spacing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_L.BoardJoin_L.calculate_even_points", "name": "calculate_even_points", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "edge_spacing", "min_spacing"], "arg_types": ["board_join_<PERSON>.<PERSON>n_L", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_even_points of BoardJoin_L", "ret_type": {".class": "Instance", "args": ["base.Point"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "common_plane": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.common_plane", "name": "common_plane", "type": "surface.Surface"}}, "complex_spacers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.<PERSON>n_L.complex_spacers", "name": "complex_spacers", "type": {".class": "Instance", "args": ["complex_spacer.ComplexSpacerLocated"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "dowels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.<PERSON>n_<PERSON>.dowels", "name": "dowels", "type": {".class": "Instance", "args": ["dowel.LocatedDowel"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "joint_surface_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.joint_surface_length", "name": "joint_surface_length", "type": "builtins.int"}}, "joint_surface_length_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.<PERSON>n_L.joint_surface_length_dimension", "name": "joint_surface_length_dimension", "type": "base.Dimension"}}, "joint_surface_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.joint_surface_width", "name": "joint_surface_width", "type": "builtins.int"}}, "joint_surface_width_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.joint_surface_width_dimension", "name": "joint_surface_width_dimension", "type": "base.Dimension"}}, "make_single_dowel_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "point", "dowel_length", "dowel_diameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_<PERSON>.make_single_dowel_join", "name": "make_single_dowel_join", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "point", "dowel_length", "dowel_diameter"], "arg_types": ["board_join_<PERSON>.<PERSON>n_L", "base.Point", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_single_dowel_join of BoardJoin_L", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_L.BoardJoin_L.name", "name": "name", "type": "builtins.str"}}, "plane_edge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.BoardJoin_L.plane_edge", "name": "plane_edge", "type": "surface.Surface"}}, "plane_face": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "board_join_<PERSON>.<PERSON>Join_L.plane_face", "name": "plane_face", "type": "surface.Surface"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board_join_L.BoardJoin_L.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board_join_<PERSON>.<PERSON>n_L", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoardJoin_L_cam_lock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board_join_<PERSON>.<PERSON>n_L"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board_join_<PERSON>.<PERSON>_L_cam_lock", "name": "BoardJoin_L_cam_lock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_L_cam_lock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "board_join_L", "mro": ["board_join_<PERSON>.<PERSON>_L_cam_lock", "board_join_<PERSON>.<PERSON>n_L", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "cam_lock_type", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_L_cam_lock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "cam_lock_type", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "arg_types": ["board_join_<PERSON>.<PERSON>_L_cam_lock", "builtins.str", "board.LocatedBoard", "board.LocatedBoard", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoardJoin_L_cam_lock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cam_lock_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "board_join_<PERSON>.<PERSON>n_L_cam_lock.cam_lock_type", "name": "cam_lock_type", "type": "builtins.str"}}, "make_single_camlock_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_L_cam_lock.make_single_camlock_join", "name": "make_single_camlock_join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "point"], "arg_types": ["board_join_<PERSON>.<PERSON>_L_cam_lock", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_single_camlock_join of BoardJoin_L_cam_lock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board_join_<PERSON>.<PERSON>n_L_cam_lock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board_join_<PERSON>.<PERSON>_L_cam_lock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoardJoin_L_confirmat_7x50": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board_join_<PERSON>.<PERSON>n_L"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board_join_<PERSON><PERSON>_L_confirmat_7x50", "name": "BoardJoin_L_confirmat_7x50", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board_join_<PERSON><PERSON>_L_confirmat_7x50", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "board_join_L", "mro": ["board_join_<PERSON><PERSON>_L_confirmat_7x50", "board_join_<PERSON>.<PERSON>n_L", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_L_confirmat_7x50.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "arg_types": ["board_join_<PERSON><PERSON>_L_confirmat_7x50", "builtins.str", "board.LocatedBoard", "board.LocatedBoard", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoardJoin_L_confirmat_7x50", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_confirmat_7x50": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_L_confirmat_7x50.add_confirmat_7x50", "name": "add_confirmat_7x50", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "location"], "arg_types": ["board_join_<PERSON><PERSON>_L_confirmat_7x50", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_confirmat_7x50 of BoardJoin_L_confirmat_7x50", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_single_confirmat_7x50_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>n_L_confirmat_7x50.make_single_confirmat_7x50_join", "name": "make_single_confirmat_7x50_join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "point"], "arg_types": ["board_join_<PERSON><PERSON>_L_confirmat_7x50", "base.Point"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_single_confirmat_7x50_join of BoardJoin_L_confirmat_7x50", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board_join_<PERSON><PERSON>_L_confirmat_7x50.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board_join_<PERSON><PERSON>_L_confirmat_7x50", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoardJoin_L_dowel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["board_join_<PERSON>.<PERSON>n_L"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "name": "BoardJoin_<PERSON>_dowel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "board_join_L", "mro": ["board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "board_join_<PERSON>.<PERSON>n_L", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "board_join_<PERSON>.<PERSON>_<PERSON>_dowel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "board_1", "board_2", "initial_distance", "distance_between_dowels", "dowel_length", "dowel_diameter"], "arg_types": ["board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "builtins.str", "board.LocatedBoard", "board.LocatedBoard", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoardJoin_L_dowel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "board_join_<PERSON>.<PERSON>_L_dowel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "board_join_<PERSON>.<PERSON>_<PERSON>_dowel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComplexSpacerLocated": {".class": "SymbolTableNode", "cross_ref": "complex_spacer.ComplexSpacerLocated", "kind": "Gdef"}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "base.Dimension", "kind": "Gdef"}, "Dowel": {".class": "SymbolTableNode", "cross_ref": "dowe<PERSON>.<PERSON>", "kind": "Gdef"}, "LocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.LocatedBoard", "kind": "Gdef"}, "LocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.LocatedDowel", "kind": "Gdef"}, "Point": {".class": "SymbolTableNode", "cross_ref": "base.Point", "kind": "Gdef"}, "Rotation": {".class": "SymbolTableNode", "cross_ref": "base.Rotation", "kind": "Gdef"}, "Side": {".class": "SymbolTableNode", "cross_ref": "base.Side", "kind": "Gdef"}, "Surface": {".class": "SymbolTableNode", "cross_ref": "surface.Surface", "kind": "Gdef"}, "XLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.XLocatedDowel", "kind": "Gdef"}, "YLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.YLocatedDowel", "kind": "Gdef"}, "ZLocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.ZLocatedDowel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "board_join_L.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "floor": {".class": "SymbolTableNode", "cross_ref": "math.floor", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\board_join_L.py"}