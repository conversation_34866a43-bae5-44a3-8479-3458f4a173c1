from dataclasses import dataclass, field
from board import LocatedBoard
from dowel import LocatedDowel
from complex_spacer import ComplexSpacerLocated


@dataclass
class Cabinet:
    """Represents a cabinet."""
    boards: list[LocatedBoard] = field(default_factory=list)
    dowels: list[LocatedDowel] = field(default_factory=list)
    complex_spacers: list[ComplexSpacerLocated] = field(default_factory=list)

    def add_board(self, board: LocatedBoard) -> None:
        self.boards.append(board)

    def add_dowel(self, dowel: LocatedDowel) -> None:
        self.dowels.append(dowel)

    def add_complex_spacer(self, complex_spacer: ComplexSpacerLocated) -> None:
        self.complex_spacers.append(complex_spacer)

    def get_board(self, name: str) -> LocatedBoard:
        for board in self.boards:
            if board.board.name == name:
                return board
        raise Exception("Board not found: " + name)

    def get_dowel(self, name: str) -> LocatedDowel:
        for dowel in self.dowels:
            if dowel.dowel.name == name:
                return dowel
        raise Exception("Dowel not found: " + name)

    def check_colisions(self) -> bool:
        result = False
        for i in range(len(self.boards)):
            board = self.boards[i]
            for j in range(i + 1, len(self.boards)):
                if board.colides_with_board(self.boards[j]):
                    print("Collision: " + board.board.name + " and " + self.boards[j].board.name)
                    result = True
            for existing_dowel in self.dowels:
                if board.colides_with_dowel(existing_dowel):
                    print("Collision: " + board.board.name + " and " + existing_dowel.dowel.name)
                    result = True
        for i in range(len(self.dowels)):
            dowel = self.dowels[i]
            for j in range(i + 1, len(self.dowels)):
                if dowel.colides_with_dowel(self.dowels[j]):
                    print("Collision: " + dowel.dowel.name + " and " + self.dowels[j].dowel.name)
                    result = True
        for complex_spacer in self.complex_spacers:
            for board in self.boards:
                if complex_spacer.colides_with_board(board):
                    print("Collision: " + complex_spacer.get_name() + " and " + board.board.name)
                    result = True
            for dowel in self.dowels:
                if complex_spacer.colides_with_dowel(dowel):
                    print("Collision: " + complex_spacer.get_name() + " and " + dowel.dowel.name)
                    result = True
        return result
