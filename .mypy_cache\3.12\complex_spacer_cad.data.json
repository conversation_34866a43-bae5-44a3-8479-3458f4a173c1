{".class": "MypyFile", "_fullname": "complex_spacer_cad", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Box": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.Box", "name": "Box", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Box", "source_any": null, "type_of_any": 3}}}, "ComplexSpacerCad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "complex_spacer_cad.ComplexSpacerCad", "name": "ComplexSpacerCad", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "complex_spacer_cad", "mro": ["complex_spacer_cad.ComplexSpacerCad", "builtins.object"], "names": {".class": "SymbolTable", "__boxes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "complex_spacer_cad.ComplexSpacerCad.__boxes", "name": "__boxes", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__cylinders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "complex_spacer_cad.ComplexSpacerCad.__cylinders", "name": "__cylinders", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__doc_object": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "complex_spacer_cad.ComplexSpacerCad.__doc_object", "name": "__doc_object", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "complex_spacer_located"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "complex_spacer_located"], "arg_types": ["complex_spacer_cad.ComplexSpacerCad", {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ComplexSpacerCad", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__make_box": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "box"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad.__make_box", "name": "__make_box", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "box"], "arg_types": ["complex_spacer_cad.ComplexSpacerCad", {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Box", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__make_box of ComplexSpacerCad", "ret_type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Part", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__make_cylinder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cylinder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad.__make_cylinder", "name": "__make_cylinder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cylinder"], "arg_types": ["complex_spacer_cad.ComplexSpacerCad", {".class": "AnyType", "missing_import_name": "complex_spacer_cad.<PERSON><PERSON><PERSON>", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__make_cylinder of ComplexSpacerCad", "ret_type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Part", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "complex_spacer_cad.ComplexSpacerCad.__name", "name": "__name", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_document_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad.get_document_object", "name": "get_document_object", "type": null}}, "instantiate_in_freecad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer_cad.ComplexSpacerCad.instantiate_in_freecad", "name": "instantiate_in_freecad", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "complex_spacer_cad.ComplexSpacerCad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "complex_spacer_cad.ComplexSpacerCad", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComplexSpacerLocated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.ComplexSpacerLocated", "name": "ComplexSpacerLocated", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.ComplexSpacerLocated", "source_any": null, "type_of_any": 3}}}, "Cylinder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.<PERSON><PERSON><PERSON>", "source_any": null, "type_of_any": 3}}}, "Dimension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.Dimension", "name": "Dimension", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Dimension", "source_any": null, "type_of_any": 3}}}, "FreeCAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.FreeCAD", "name": "FreeCAD", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.FreeCAD", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Part": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.Part", "name": "Part", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Part", "source_any": null, "type_of_any": 3}}}, "Vector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "complex_spacer_cad.Vector", "name": "Vector", "type": {".class": "AnyType", "missing_import_name": "complex_spacer_cad.Vector", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer_cad.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\complex_spacer_cad.py"}