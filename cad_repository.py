import sys
import os
import FreeCAD  # type: ignore

# Setup path and import local modules
sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
from board_cad import BoardCad  # noqa: E402
from complex_spacer_cad import ComplexSpacerCad  # noqa: E402
from dowel_cad import DowelCad  # noqa: E402
from cabinet import Cabinet  # type: ignore  # noqa: E402
from cabinet_loader import CabinetLoader, Evaluator  # type: ignore  # noqa: E402


class CadRepository:
    def __init__(self):
        self.__items = {}
        self.__cabinet = Cabinet()

    def instantiate_in_freecad(self):
        for item in self.__items.values():
            item.instantiate_in_freecad()
        for name, item in ((name, item) for name, item in self.__items.items() if name.find(".") != -1):
            folder_name = name.split(".")[0]
            folder = FreeCAD.ActiveDocument.getObject(folder_name)
            if not folder:
                folder = FreeCAD.ActiveDocument.addObject("App::DocumentObjectGroup", folder_name)
            folder.addObject(item.get_document_object())

        # Ustaw widok izometryczny (góra-przód-prawa) i dopasuj do obiektów

    def set_view(self):
        try:
            import FreeCADGui  # type: ignore
            FreeCADGui.activeDocument().activeView().viewIsometric()
            FreeCADGui.SendMsgToActiveView("ViewFit")
        except ImportError:
            # FreeCADGui not available (headless mode)
            pass

    def get_board_cad(self, name) -> BoardCad:
        return self.__items.get(name)

    def load_cabinet(self, file_path: str):
        # from board_cad import BoardCad
        self.__items = {}
        self.__cabinet = Cabinet()
        loader = CabinetLoader()
        self.__cabinet = loader.load_from_file(file_path, evaluator=Evaluator())
        self.__cabinet.check_colisions()
        for board in self.__cabinet.boards:
            self.__items[board.board.name] = BoardCad(board)
        for dowel in self.__cabinet.dowels:
            self.__items[dowel.dowel.name] = DowelCad(dowel)
        for complex_spacer in self.__cabinet.complex_spacers:
            self.__items[complex_spacer.get_name()] = ComplexSpacerCad(complex_spacer)
