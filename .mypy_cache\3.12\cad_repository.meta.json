{"data_mtime": 1761030083, "dep_lines": [1, 2, 7, 8, 9, 1, 1, 1, 1, 3, 10, 11, 33], "dep_prios": [10, 10, 5, 5, 5, 5, 30, 30, 30, 10, 5, 5, 20], "dependencies": ["sys", "os", "board_cad", "complex_spacer_cad", "dowel_cad", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "42a0f25da9a819d1ba40d9b848a0665c3b3c48e8", "id": "cad_repository", "ignore_all": true, "interface_hash": "0ea7dfc7d3cb1b17bc2a73fe16f50a5d86969e75", "mtime": 1761030095, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\cad_repository.py", "plugin_data": null, "size": 2298, "suppressed": ["FreeCAD", "cabinet", "cabinet_loader", "FreeCADGui"], "version_id": "1.15.0"}