from abc import ABC, abstractmethod
from dataclasses import dataclass
from base import Point, Dimension, Rotation
from box import Box


@dataclass
class Cylinder(ABC):
    radius: float
    height: float
    center: Point

    def is_empty(self):
        return self.radius <= 0 or self.height <= 0

    @abstractmethod
    def __add__(self, other: Point) -> "Cylinder":
        pass

    @abstractmethod
    def __sub__(self, other: Point) -> "Cylinder":
        pass

    @abstractmethod
    def _get_axis_aligned_bounding_box(self) -> Box:
        pass

    def overlaps_with_cylinder(self, other: "Cylinder") -> bool:
        self_box = self._get_axis_aligned_bounding_box()
        other_box = other._get_axis_aligned_bounding_box()
        intersection = self_box.intersection_with_box(other_box)
        return not intersection.is_empty()

    @abstractmethod
    def intersection_with_box(self, box: Box) -> "Cylinder":
        pass

    @abstractmethod
    def fully_contains(self, other: "Cylinder") -> bool:
        pass

    @abstractmethod
    def rotate_90_clockwise_along_z(self) -> "Cylinder":
        pass

    @abstractmethod
    def rotate_180_clockwise_along_z(self) -> "Cylinder":
        pass

    @abstractmethod
    def rotate_270_clockwise_along_z(self) -> "Cylinder":
        pass

    def rotate(self, rotate_point: "Point", rotation: Rotation) -> "Cylinder":
        if rotation == Rotation.ROTATE_CLOCKWISE_90:
            return self.rotate_90_clockwise_along_z()+rotate_point
        elif rotation == Rotation.ROTATE_180:
            return self.rotate_180_clockwise_along_z()+rotate_point
        elif rotation == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            return self.rotate_270_clockwise_along_z()+rotate_point
        else:
            return self+rotate_point

    @staticmethod
    def cylinder_factory(radius: float, height: float, center: Point, dimension: Dimension) -> "Cylinder":
        if dimension == Dimension.X:
            return XCylinder(radius, height, center)
        elif dimension == Dimension.Y:
            return YCylinder(radius, height, center)
        elif dimension == Dimension.Z:
            return ZCylinder(radius, height, center)
        else:
            raise ValueError("Invalid dimension")

    @abstractmethod
    def get_axis(self) -> Dimension:
        pass


class XCylinder(Cylinder):

    def intersection_with_box(self, box: Box) -> "XCylinder":
        if self.center.x > box.x_max or self.center.x + self.height < box.x_min:
            return XCylinder(0, 0, self.center)
        if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
            return XCylinder(0, 0, self.center)
        if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
            return XCylinder(0, 0, self.center)
        x_min = max(box.x_min, self.center.x)
        x_max = min(box.x_max, self.center.x + self.height)
        return XCylinder(self.radius, x_max - x_min, Point(x_min, self.center.y, self.center.z))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.y != other.center.y or self.center.z != other.center.z:
            return False
        if self.center.x > other.center.x or self.center.x + self.height < other.center.x + other.height:
            return False
        return True

    def _get_axis_aligned_bounding_box(self) -> Box:
        return Box(self.center.x,
                   self.center.x + self.height,
                   self.center.y - self.radius,
                   self.center.y + self.radius,
                   self.center.z - self.radius,
                   self.center.z + self.radius)

    def rotate_90_clockwise_along_z(self) -> "YCylinder":
        return YCylinder(self.radius, self.height,
                         self.center.rotate_90_clockwise_along_z()+Point(0, -self.height, 0))

    def rotate_180_clockwise_along_z(self) -> "XCylinder":
        new_center = self.center.rotate_180_clockwise_along_z() + Point(-self.height, 0, 0)
        return XCylinder(self.radius, self.height, new_center)

    def rotate_270_clockwise_along_z(self) -> "YCylinder":
        return YCylinder(self.radius, self.height,
                         self.center.rotate_270_clockwise_along_z())

    def __add__(self, other: Point) -> "XCylinder":
        return XCylinder(self.radius, self.height, self.center + other)

    def __sub__(self, other: Point) -> "XCylinder":
        return XCylinder(self.radius, self.height, self.center - other)

    def get_axis(self) -> Dimension:
        return Dimension.X


class YCylinder(Cylinder):
    def intersection_with_box(self, box: Box) -> "YCylinder":
        if self.center.y > box.y_max or self.center.y + self.height < box.y_min:
            return YCylinder(0, 0, self.center)
        if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
            return YCylinder(0, 0, self.center)
        if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
            return YCylinder(0, 0, self.center)
        y_min = max(box.y_min, self.center.y)
        y_max = min(box.y_max, self.center.y + self.height)
        return YCylinder(self.radius, y_max - y_min, Point(self.center.x, y_min, self.center.z))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.x != other.center.x or self.center.z != other.center.z:
            return False
        if self.center.y > other.center.y or self.center.y + self.height < other.center.y + other.height:
            return False
        return True

    def _get_axis_aligned_bounding_box(self) -> Box:
        return Box(self.center.x - self.radius,
                   self.center.x + self.radius,
                   self.center.y, self.center.y + self.height,
                   self.center.z - self.radius,
                   self.center.z + self.radius)

    def rotate_90_clockwise_along_z(self) -> XCylinder:
        return XCylinder(self.radius, self.height,
                         self.center.rotate_90_clockwise_along_z())

    def rotate_180_clockwise_along_z(self) -> "YCylinder":
        return YCylinder(self.radius, self.height,
                         self.center.rotate_180_clockwise_along_z()+Point(0, -self.height, 0))

    def rotate_270_clockwise_along_z(self) -> XCylinder:
        return XCylinder(self.radius, self.height,
                         self.center.rotate_270_clockwise_along_z()+Point(-self.height, 0, 0))

    def __add__(self, other: Point) -> "YCylinder":
        return YCylinder(self.radius, self.height, self.center + other)

    def __sub__(self, other: Point) -> "YCylinder":
        return YCylinder(self.radius, self.height, self.center - other)

    def get_axis(self) -> Dimension:
        return Dimension.Y


class ZCylinder(Cylinder):
    def intersection_with_box(self, box: Box) -> "ZCylinder":
        if self.center.z > box.z_max or self.center.z + self.height < box.z_min:
            return ZCylinder(0, 0, self.center)
        if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
            return ZCylinder(0, 0, self.center)
        if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
            return ZCylinder(0, 0, self.center)
        z_min = max(box.z_min, self.center.z)
        z_max = min(box.z_max, self.center.z + self.height)
        return ZCylinder(self.radius, z_max - z_min, Point(self.center.x, self.center.y, z_min))

    def fully_contains(self, other: Cylinder) -> bool:
        if type(self) is not type(other):
            return False
        if self.radius != other.radius:
            return False
        if self.center.x != other.center.x or self.center.y != other.center.y:
            return False
        if self.center.z > other.center.z or self.center.z + self.height < other.center.z + other.height:
            return False
        return True

    def _get_axis_aligned_bounding_box(self) -> Box:
        return Box(self.center.x - self.radius,
                   self.center.x + self.radius,
                   self.center.y - self.radius,
                   self.center.y + self.radius,
                   self.center.z,
                   self.center.z + self.height)

    def rotate_90_clockwise_along_z(self) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_90_clockwise_along_z())

    def rotate_180_clockwise_along_z(self) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_180_clockwise_along_z())

    def rotate_270_clockwise_along_z(self) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center.rotate_270_clockwise_along_z())

    def __add__(self, other: Point) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center + other)

    def __sub__(self, other: Point) -> "ZCylinder":
        return ZCylinder(self.radius, self.height, self.center - other)

    def get_axis(self) -> Dimension:
        return Dimension.Z
