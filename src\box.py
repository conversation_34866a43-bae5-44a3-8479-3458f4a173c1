from base import Rotation, Point, Dimension
from dataclasses import dataclass


@dataclass
class Box():
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

    def __post_init__(self):
        if self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max:
            self.x_min = 0
            self.x_max = 0
            self.y_min = 0
            self.y_max = 0
            self.z_min = 0
            self.z_max = 0

    def __add__(self, other: "Point") -> "Box":
        return Box(self.x_min + other.x, self.x_max + other.x,
                   self.y_min + other.y, self.y_max + other.y,
                   self.z_min + other.z, self.z_max + other.z)

    def __sub__(self, other: "Point") -> "Box":
        return Box(self.x_min - other.x, self.x_max - other.x,
                   self.y_min - other.y, self.y_max - other.y,
                   self.z_min - other.z, self.z_max - other.z)

    def is_empty(self) -> bool:
        return self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max

    def get_range_in_dimension(self, dimension: "Dimension") -> tuple[float, float]:
        if dimension == Dimension.X:
            return (self.x_min, self.x_max)
        elif dimension == Dimension.Y:
            return (self.y_min, self.y_max)
        elif dimension == Dimension.Z:
            return (self.z_min, self.z_max)
        else:
            raise ValueError("Invalid dimension")

    def intersection_with_box(self, other: "Box") -> "Box":
        x_min = max(self.x_min, other.x_min)
        x_max = min(self.x_max, other.x_max)
        y_min = max(self.y_min, other.y_min)
        y_max = min(self.y_max, other.y_max)
        z_min = max(self.z_min, other.z_min)
        z_max = min(self.z_max, other.z_max)
        return Box(x_min, x_max, y_min, y_max, z_min, z_max)

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Box):
            return NotImplemented
        return self.x_min == other.x_min and self.x_max == other.x_max and\
            self.y_min == other.y_min and self.y_max == other.y_max and\
            self.z_min == other.z_min and self.z_max == other.z_max

    def __str__(self):
        return f"Box({self.x_min} - {self.x_max}, {self.y_min} - {self.y_max}, {self.z_min} - {self.z_max})"

    def fully_contains(self, other: "Box") -> bool:
        return self.x_min <= other.x_min and self.x_max >= other.x_max and\
            self.y_min <= other.y_min and self.y_max >= other.y_max and\
            self.z_min <= other.z_min and self.z_max >= other.z_max

    def rotate(self, rotate_point: "Point", rotation: Rotation) -> "Box":
        if rotation == Rotation.ROTATE_CLOCKWISE_90:
            return self.rotate_90_clockwise_along_z()+rotate_point
        elif rotation == Rotation.ROTATE_180:
            return self.rotate_180_clockwise_along_z()+rotate_point
        elif rotation == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            return self.rotate_270_clockwise_along_z()+rotate_point
        else:
            return self+rotate_point

    def rotate_90_clockwise_along_z(self) -> "Box":
        return Box(self.y_min, self.y_max,
                   - self.x_max, - self.x_min,
                   self.z_min, self.z_max)

    def rotate_180_clockwise_along_z(self) -> "Box":
        return Box(- self.x_max, - self.x_min,
                   - self.y_max, - self.y_min,
                   self.z_min, self.z_max)

    def rotate_270_clockwise_along_z(self) -> "Box":
        return Box(- self.y_max, - self.y_min,
                   self.x_min, self.x_max,
                   self.z_min, self.z_max)
