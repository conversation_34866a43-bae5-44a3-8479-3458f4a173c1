# Temporarily disable problematic imports to avoid MRO and circular dependency issues
# from src.physical_item import NamedItem, LocatedBoard, Orientation
# from src.base import NamedItem
# from src.physical_item import LocatedBoard, Orientation, LocatedPhysicalItem
from typing import Optional
import FreeCAD
from FreeCAD import Vector
import Part
from abc import ABC, abstractmethod
import sys
import os


sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
# sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
from dowel import LocatedDowel
from base import Dimension


class DowelCad:
    def __init__(self, located_dowel: LocatedDowel):
        self.__located_dowel = located_dowel
        self.__doc_object = None

    def get_document_object(self):
        return self.__doc_object

    def instantiate_in_freecad(self):
        if self.__doc_object is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part

        dowel = self.__located_dowel.get_cylinder()
        shape = Part.makeCylinder(dowel.radius, dowel.height)
        shape.translate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z))
        if dowel.get_axis() == Dimension.X:
            shape.rotate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z), FreeCAD.Vector(0, 1, 0), 90)
        elif dowel.get_axis() == Dimension.Y:
            shape.rotate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z), FreeCAD.Vector(-1, 0, 0), 90)
        obj = doc.addObject("Part::Feature", self.__located_dowel.dowel.name)
        obj.Shape = shape
        doc.recompute()
        obj.Label = self.__located_dowel.dowel.name
        self.__doc_object = obj
