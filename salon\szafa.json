{"$schema": "../cabinet_schema.json", "required_variables": [], "variables": {"width": 770, "height": "2715-100", "depth": 580, "side_width": 340, "bottom_height": 1530, "door_gap": 3, "top_shelf": 400, "side_height": "height-bottom_height", "polka_2_height": "height-side_height+round((side_height-top_shelf)*0.4)", "polka_srodek": "1000+18"}, "boards": [{"name": "lewa_sciana", "width": "depth", "height": "height-36", "thickness": 18, "orientation": "SIDE", "location": {"x": 0, "y": 0, "z": 18}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "dol", "width": "width", "height": "depth", "thickness": 18, "orientation": "FLAT", "location": {"x": 0, "y": 0, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}, {"name": "prawa_sciana", "width": "depth-3", "height": "height-18-side_height", "thickness": 18, "orientation": "SIDE", "location": {"x": "width-18", "y": 0, "z": 18}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "gora", "width": "width+side_width", "height": "depth-3", "thickness": 18, "orientation": "FLAT", "location": {"x": 0, "y": 0, "z": "height-18"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "side_dol", "width": "side_width+18", "height": "depth-3", "thickness": 18, "orientation": "FLAT", "location": {"x": "width-18", "y": 0, "z": "height-side_height"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "side_prawa", "width": "depth-3", "height": "side_height-36", "thickness": 18, "orientation": "SIDE", "location": {"x": "width+side_width-18", "y": 0, "z": "height-side_height+18"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "side_front", "width": "side_width-1", "height": "side_height", "thickness": 18, "orientation": "FRONT", "location": {"x": "width+1", "y": "-18", "z": "height-side_height"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "door_bottom", "width": "width-door_gap*2", "height": "bottom_height-door_gap*2", "thickness": 18, "orientation": "FRONT", "location": {"x": "door_gap", "y": -18, "z": "door_gap"}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}, {"name": "door_top", "width": "width-door_gap*2", "height": "side_height-10", "thickness": 18, "orientation": "FRONT", "location": {"x": "door_gap", "y": -18, "z": "bottom_height"}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}, {"name": "polka", "width": "width+side_width-2*18", "height": "depth-3", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": 0, "z": "height-top_shelf-18"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "polka2", "width": "side_width-18", "height": "depth-18-3", "thickness": 18, "orientation": "FLAT", "location": {"x": "width", "y": 0, "z": "polka_2_height"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "tylne_wsparcie", "width": "side_width", "height": "side_height-top_shelf-36", "thickness": 18, "orientation": "FRONT", "location": {"x": "width-18", "y": "depth-18-3", "z": "height-side_height+18"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "plecy_dolne", "width": "width-13-1", "height": "height-top_shelf-18-8+8", "thickness": 3, "orientation": "FRONT", "location": {"x": 13, "y": "depth-3", "z": 8}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "plecy_gorne", "width": "width+side_width-13-1", "height": "top_shelf+8", "thickness": 3, "orientation": "FRONT", "location": {"x": 13, "y": "depth-3", "z": "height-top_shelf-8"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "polka_srodek", "width": "width-36", "height": "depth-10", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": 7, "z": "polka_srodek"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "NONE"}], "include": [{"name": "z<PERSON><PERSON>_szafy_1z6", "file_path": "../narzedzia/zawias_rownolegly_prawy.json", "rotation": "NONE", "reference_location": {"x": "width+1", "y": 0, "z": "(height-15)-50"}, "variables": {}}, {"name": "z<PERSON><PERSON>_szafy_2z6", "file_path": "../narzedzia/zawias_rownolegly_prawy.json", "rotation": "NONE", "reference_location": {"x": "width+1", "y": 0, "z": "bottom_height+side_height/2"}, "variables": {}}, {"name": "z<PERSON><PERSON>_szafy_3z5", "file_path": "../narzedzia/zawias_rownolegly_prawy.json", "rotation": "NONE", "reference_location": {"x": "width+1", "y": 0, "z": "bottom_height+60"}, "variables": {}}, {"name": "z<PERSON><PERSON>_szafy_4z6", "file_path": "../narzedzia/zawias_nachodzacy_prawy.json", "rotation": "NONE", "reference_location": {"x": "width-18", "y": 0, "z": "bottom_height-60"}, "variables": {}}, {"name": "z<PERSON><PERSON>_szafy_5z6", "file_path": "../narzedzia/zawias_nachodzacy_prawy.json", "rotation": "NONE", "reference_location": {"x": "width-18", "y": 0, "z": "bottom_height/2"}, "variables": {}}, {"name": "z<PERSON><PERSON>_szafy_6z6", "file_path": "../narzedzia/zawias_nachodzacy_prawy.json", "rotation": "NONE", "reference_location": {"x": "width-18", "y": 0, "z": "60"}, "variables": {}}], "L_joins": [{"name": "dowels_1", "joint_type": "confirmat_7x50", "board_edge_name": "lewa_sciana", "board_face_name": "dol"}, {"name": "dowels_2", "joint_type": "confirmat_7x50", "board_edge_name": "lewa_sciana", "board_face_name": "polka"}, {"name": "dowels_3", "joint_type": "confirmat_7x50", "board_edge_name": "lewa_sciana", "board_face_name": "gora"}]}