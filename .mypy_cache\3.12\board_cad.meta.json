{"data_mtime": 1761029596, "dep_lines": [1, 5, 6, 1, 1, 1, 2, 4, 9, 10, 11], "dep_prios": [5, 10, 10, 5, 30, 30, 5, 10, 5, 5, 5], "dependencies": ["typing", "sys", "os", "builtins", "_frozen_importlib", "abc"], "hash": "a7c0ddbb14064275d80c230fb044a620dd4527f0", "id": "board_cad", "ignore_all": false, "interface_hash": "1cbdcee64520c27c22bb5fac9d584586a52ffa0f", "mtime": 1761030485, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\board_cad.py", "plugin_data": null, "size": 5209, "suppressed": ["FreeCAD", "Part", "base", "surface", "board"], "version_id": "1.15.0"}