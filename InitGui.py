import FreeCAD  # type: ignore
import os
import FreeCADGui  # type: ignore
from FreeCADGui import Workbench  # type: ignore


class CabinetPlaner (Workbench):

    MenuText = "Cabinet planner"
    ToolTip = "A description of my workbench"
    Icon = os.path.join(FreeCAD.getUserAppDataDir(), 'Mod', 'CabinetPlanner', 'resources', 'icons', 'my_icon.svg')

    def Initialize(self):
        """This function is executed when the workbench is first activated.
        It is executed once in a FreeCAD session followed by the Activated function.
        """
        self.list = ["LoadCabinet", "ReloadCabinet"]  # a list of command names created in the line above
        self.appendToolbar("Cabinets", self.list)  # creates a new toolbar with your commands
        self.appendMenu("Cabinets", self.list)  # creates a new menu

    def Activated(self):
        """This function is executed whenever the workbench is activated"""
        return

    def Deactivated(self):
        """This function is executed whenever the workbench is deactivated"""
        return

    def ContextMenu(self, recipient):
        """This function is executed whenever the user right-clicks on screen"""
        # "recipient" will be either "view" or "tree"
        self.appendContextMenu("My commands", self.list)  # add commands to the context menu

    def GetClassName(self):
        # This function is mandatory if this is a full Python workbench
        # This is not a template, the returned string should be exactly "Gui::PythonWorkbench"
        return "Gui::PythonWorkbench"


workbench = CabinetPlaner()
FreeCADGui.addWorkbench(workbench)

last_file = None
last_folder = None


class LoadCabinet:
    def __init__(self):
        pass

    def GetResources(self):
        return {
            'Pixmap': os.path.join(FreeCAD.getUserAppDataDir(),
                                   'Mod', 'CabinetPlanner', 'resources', 'icons', 'my_icon.svg'),
            # 'Pixmap': os.path.join(os.path.dirname(__file__),
            # "../CabinetPlanner/", 'resources', 'icons', 'my_icon.svg'),
            'MenuText': 'Zaladuj szafkę',
            'ToolTip': 'Kliknij wskaż plik z definicją szafy'
        }

    def Activated(self):
        # global global_cad_repository
        global last_file
        global last_folder
        # Create a dialog
        from PySide import QtGui, QtCore  # type: ignore
        if last_folder is None:
            last_folder = QtCore.QStandardPaths.writableLocation(QtCore.QStandardPaths.DocumentsLocation)
        # from src.board import Board, LocatedBoard
        # from src.base import Orientation, Point
        # from board import Board, LocatedBoard
        # from base import Orientation, Point
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Wskaż plik źródłowy")
        dialog.setMinimumWidth(450)
        dialog.setMinimumHeight(450)
        layout = QtGui.QVBoxLayout(dialog)
        form_layout = QtGui.QFormLayout()

        name_box = QtGui.QFileDialog()
        name_box.setNameFilter("Pliki JSON (*.json)")
        name_box.setDirectory(last_folder)

        form_layout.addRow("Plik z definicją szafy:", name_box)

        layout.addLayout(form_layout)

        button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Ok | QtGui.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        result = dialog.exec_()

        if result == QtGui.QDialog.Accepted:
            last_file = name_box.selectedFiles()[0]
            last_folder = os.path.dirname(last_file)
            from cad_repository import CadRepository  # type: ignore
            global_cad_repository = CadRepository()
            global_cad_repository.load_cabinet(last_file)
            global_cad_repository.instantiate_in_freecad()
            global_cad_repository.set_view()

    def IsActive(self):
        return True


class ReloadCabinet:
    def __init__(self):
        pass

    def GetResources(self):
        return {
            'Pixmap': os.path.join(FreeCAD.getUserAppDataDir(),
                                   'Mod', 'CabinetPlanner', 'resources', 'icons', 'reload_icon.svg'),
            'MenuText': 'Załaduj ponownie tę samą szafkę',
            'ToolTip': 'Kliknij aby załadować ostatnio wczytaną szafkę'
        }

    def Activated(self):
        # global global_cad_repository
        global last_file

        # Wyczyść log FreeCAD
        from PySide import QtGui  # type: ignore
        mw = FreeCADGui.getMainWindow()
        report_view = mw.findChild(QtGui.QDockWidget, "Report view")
        if report_view:
            report_view.widget().clear()

        for obj in FreeCAD.ActiveDocument.Objects:
            FreeCAD.ActiveDocument.removeObject(obj.Name)
        from cad_repository import CadRepository
        global_cad_repository = CadRepository()
        global_cad_repository.load_cabinet(last_file)
        global_cad_repository.instantiate_in_freecad()

    def IsActive(self):
        global last_file
        return last_file is not None


# Register commands at module level
FreeCADGui.addCommand('LoadCabinet', LoadCabinet())
FreeCADGui.addCommand('ReloadCabinet', ReloadCabinet())
