from board import LocatedBoard
from dowel import Dowel, LocatedDowel, XLocatedDowel, YLocatedDowel, ZLocatedDowel
from base import Side, Dimension, Point, Rotation
from surface import Surface
from complex_spacer import ComplexSpacerLocated
from math import floor


class BoardJoin_L:
    name: str
    board_edge: LocatedBoard
    board_face: LocatedBoard
    board_edge_side: Side
    board_face_side: Side
    common_plane: Surface
    plane_edge: Surface
    plane_face: Surface
    dowels: list[LocatedDowel]
    complex_spacers: list[ComplexSpacerLocated]
    joint_surface_length: int
    joint_surface_length_dimension: Dimension
    joint_surface_width: int
    joint_surface_width_dimension: Dimension

    def __init__(self, name: str, board_1: LocatedBoard, board_2: LocatedBoard):
        self.name = name
        self.dowels = []
        self.complex_spacers = []
        for side_1 in Side:
            plane_1, _ = board_1.get_side(side_1)
            for side_2 in Side:
                if ((side_1.is_edge_side() and side_2.is_edge_side()) or
                        (side_1.is_face_side() and side_2.is_face_side())):
                    continue
                plane_2, _ = board_2.get_side(side_2)
                if not plane_1.overlaps_with_surface(plane_2):
                    continue
                if side_1.is_face_side() and side_2.is_edge_side():
                    self.board_edge = board_2
                    self.board_face = board_1
                    self.board_edge_side = side_2
                    self.board_face_side = side_1
                    self.common_plane = plane_1.get_common_surface(plane_2)
                    self.plane_edge = plane_2
                    self.plane_face = plane_1
                    self.joint_surface_length, self.joint_surface_length_dimension = \
                        self.common_plane.get_surface_length()
                    self.joint_surface_width, self.joint_surface_width_dimension = \
                        self.common_plane.get_surface_width()
                    return
                if side_2.is_face_side() and side_1.is_edge_side():
                    self.board_edge = board_1
                    self.board_face = board_2
                    self.board_edge_side = side_1
                    self.board_face_side = side_2
                    self.common_plane = plane_1.get_common_surface(plane_2)
                    self.plane_edge = plane_1
                    self.plane_face = plane_2
                    self.joint_surface_length, self.joint_surface_length_dimension = \
                        self.common_plane.get_surface_length()
                    self.joint_surface_width, self.joint_surface_width_dimension = \
                        self.common_plane.get_surface_width()
                    return
        raise ValueError("Boards do not form an L shape")

    def add_dowel(self, location: Point, dowel_length: int, dowel_diameter: int, iterator: int) -> None:
        face_depth = round(self.board_face.board.thickness/3*2)
        if self.plane_edge.depth > 0:
            dowel_side = self.board_edge_side
            dowel_plane = self.plane_edge
        else:
            dowel_side = self.board_face_side
            dowel_plane = self.plane_face
        if dowel_side.is_edge_side():
            dowel_depth = dowel_length-face_depth
        else:
            dowel_depth = face_depth
        dowel: LocatedDowel
        if dowel_plane.normal == Dimension.X:
            dowel = XLocatedDowel(Dowel(f"{self.name}_d{iterator}", dowel_length, dowel_diameter),
                                  Point(location.x-dowel_depth, location.y, location.z))
        elif dowel_plane.normal == Dimension.Y:
            dowel = YLocatedDowel(Dowel(f"{self.name}_d{iterator}", dowel_length, dowel_diameter),
                                  Point(location.x, location.y-dowel_depth, location.z))
        elif dowel_plane.normal == Dimension.Z:
            dowel = ZLocatedDowel(Dowel(f"{self.name}_d{iterator}", dowel_length, dowel_diameter),
                                  Point(location.x, location.y, location.z-dowel_depth))
        self.dowels.append(dowel)

    def calculate_even_points(self, edge_spacing: int, min_spacing: int) -> list[Point]:
        usable_length: int = self.joint_surface_length - 2*edge_spacing
        if usable_length <= 0:
            raise ValueError("Joint surface is too short")
        count_of_distances: int = floor(usable_length/min_spacing)
        if count_of_distances > 1:
            distance: float = usable_length / (count_of_distances + 1e-6)
        else:
            distance = usable_length
        distances = [edge_spacing + round(i*distance) for i in range(count_of_distances+1)]
        points: list[Point] = []
        for distance in distances:
            coordinates: dict[Dimension, float] = {}
            for dimension in Dimension:
                if dimension == self.joint_surface_length_dimension:
                    coordinates[dimension] = self.common_plane.start_point.get_coordinate(dimension) + \
                        round(distance)
                elif dimension == self.joint_surface_width_dimension:
                    coordinates[dimension] = self.common_plane.start_point.get_coordinate(dimension) + \
                        self.joint_surface_width/2
                else:
                    coordinates[dimension] = self.common_plane.start_point.get_coordinate(dimension)
            points.append(Point.create_point_from_dimension_values(coordinates))
        return points

    def make_single_dowel_join(self, point: Point, dowel_length: int, dowel_diameter: int) -> None:
        face_depth = round(self.board_face.board.thickness/3*2)
        self.board_edge.add_hole_at_position(dowel_diameter, dowel_length - face_depth + 3, self.board_edge_side, point)
        self.board_face.add_hole_at_position(dowel_diameter, face_depth, self.board_face_side, point)
        self.add_dowel(point, dowel_length, dowel_diameter, len(self.dowels))


class BoardJoin_L_dowel(BoardJoin_L):
    def __init__(self, name: str, board_1: LocatedBoard, board_2: LocatedBoard,
                 initial_distance: int = 40, distance_between_dowels: int = 64, dowel_length: int = 35,
                 dowel_diameter: int = 8):
        super().__init__(name, board_1, board_2)
        for point in self.calculate_even_points(initial_distance, distance_between_dowels):
            self.make_single_dowel_join(point, dowel_length, dowel_diameter)


class BoardJoin_L_confirmat_7x50(BoardJoin_L):
    def __init__(self, name: str, board_1: LocatedBoard, board_2: LocatedBoard,
                 initial_distance: int = 40, distance_between_dowels: int = 64,
                 dowel_length: int = 35, dowel_diameter: int = 8):
        super().__init__(name, board_1, board_2)
        positions = self.calculate_even_points(initial_distance, distance_between_dowels)
        for i in range(len(positions)):
            if i < floor(len(positions)/2):
                for point in [positions[i], positions[-1-i]]:
                    if i % 2 == 0:
                        self.make_single_dowel_join(point, dowel_length, dowel_diameter)
                    else:
                        self.make_single_confirmat_7x50_join(point)
            elif len(positions) % 2 == 1 and i < len(positions)/2:
                point = positions[i]
                if i % 2 == 0:
                    self.make_single_dowel_join(point, dowel_length, dowel_diameter)
                else:
                    self.make_single_confirmat_7x50_join(point)
            else:
                break

    def make_single_confirmat_7x50_join(self, point: Point) -> None:
        self.board_edge.add_hole_at_position(4, 50-18+3, self.board_edge_side, point)
        self.board_face.add_hole_at_position(8, -1, self.board_face_side, point)
        self.add_confirmat_7x50(point)

    def add_confirmat_7x50(self, location: Point) -> None:
        if self.common_plane.normal == Dimension.X:
            if self.plane_edge.depth < 0:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_front(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.ROTATE_COUNTER_CLOCKWISE_90))
            else:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_front(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.ROTATE_CLOCKWISE_90))
        elif self.common_plane.normal == Dimension.Y:
            if self.plane_edge.depth < 0:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_front(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.ROTATE_180))
            else:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_front(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.NONE))
        elif self.common_plane.normal == Dimension.Z:
            if self.plane_edge.depth < 0:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_up(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.NONE))
            else:
                self.complex_spacers.append(ComplexSpacerLocated.confirmat_7x50_down(
                                                f"{self.name}_c{len(self.complex_spacers)}",
                                                location,
                                                Rotation.NONE))
        else:
            raise ValueError("Invalid common plane")


class BoardJoin_L_cam_lock(BoardJoin_L):
    def __init__(self, name: str, board_1: LocatedBoard, board_2: LocatedBoard, cam_lock_type: str,
                 initial_distance: int = 40, distance_between_dowels: int = 64,
                 dowel_length: int = 35, dowel_diameter: int = 8):
        super().__init__(name, board_1, board_2)
        self.cam_lock_type = cam_lock_type
        positions = self.calculate_even_points(initial_distance, distance_between_dowels)
        for i in range(len(positions)):
            print(f"Creating cam lock join {name} {i} with type {cam_lock_type}")
            if i < floor(len(positions)/2):
                for point in [positions[i], positions[-1-i]]:
                    if i % 2 == 0:
                        print(f"Creating dowel join {name} {i} with type {cam_lock_type}")
                        self.make_single_dowel_join(point, dowel_length, dowel_diameter)
                    else:
                        print(f"Creating cam lock join from the end {name} {i} with type {cam_lock_type}")
                        self.make_single_camlock_join(point)
            elif len(positions) % 2 == 1 and i < len(positions)/2:
                point = positions[i]
                if i % 2 == 0:
                    print(f"Creating dowel join middle {name} {i} with type {cam_lock_type}")
                    self.make_single_dowel_join(point, dowel_length, dowel_diameter)
                else:
                    print(f"Creating cam lock join middle {name} {i} with type {cam_lock_type}")
                    self.make_single_camlock_join(point)
            else:
                break

    def make_single_camlock_join(self, point: Point) -> None:
        print("Hole 1")
        self.board_edge.add_hole_at_position(8, 34, self.board_edge_side, point)
        print("Hole 2")
        self.board_face.add_hole_at_position(5, 10, self.board_face_side, point)
        print("Hole 3")
        if self.cam_lock_type == "right_down":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(34, 0, -9).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "right_up":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(34, 0, 9).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "right_front":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(34, -9, 0).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "right_back":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(34, 9, 0).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_left(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "left_down":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(-34, 0, -9).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "left_up":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(-34, 0, 9).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "left_front":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(-34, -9, 0).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_left(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "left_back":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(-34, 9, 0).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_right(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "up_front":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(0, -9, 34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "up_back":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(0, 9, 34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "up_left":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(-9, 0, 34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_CLOCKWISE_90,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "up_right":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(9, 0, 34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_up(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_COUNTER_CLOCKWISE_90,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "down_front":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(0, -9, -34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.NONE,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "down_back":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(0, 9, -34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_180,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "down_left":
            self.board_edge.add_hole_at_position(15, 13, Side.BACK,
                                                 point+Point(-9, 0, -34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_CLOCKWISE_90,
                self.board_face.board.thickness))
        elif self.cam_lock_type == "down_right":
            self.board_edge.add_hole_at_position(15, 13, Side.FRONT,
                                                 point+Point(9, 0, -34).rotate(self.board_edge.reference_rotation))
            self.complex_spacers.append(ComplexSpacerLocated.cam_lock_down(
                f"{self.name}_l{len(self.complex_spacers)}",
                point,
                Rotation.ROTATE_COUNTER_CLOCKWISE_90,
                self.board_face.board.thickness))
        else:
            raise ValueError("Invalid cam lock type")
