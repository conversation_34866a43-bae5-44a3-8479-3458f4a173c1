{"$schema": "cabinet_schema.json", "variables": {}, "boards": [], "dowels": [{"name": "base", "length": 30, "diameter": 8, "orientation": "FRONT", "location": {"x": 0, "y": 0, "z": 10}}, {"name": "reference", "length": 20, "diameter": 6, "orientation": "FRONT", "location": {"x": 50, "y": 50, "z": 0}}, {"name": "base_north", "length": 20, "diameter": 6, "orientation": "FLAT", "location": {"x": 0, "y": 5, "z": 0}}, {"name": "base_west", "length": 20, "diameter": 6, "orientation": "SIDE", "location": {"x": 5, "y": 0, "z": 0}}, {"name": "base_east", "length": 20, "diameter": 6, "orientation": "SIDE", "location": {"x": -25, "y": 0, "z": 0}}], "complex_spacers": [], "include": [{"name": "incln", "file_path": "test_include.json", "rotation": "NONE", "reference_location": {"x": 50, "y": 50, "z": 0}, "variables": {}}, {"name": "incle", "file_path": "test_include.json", "rotation": "ROTATE_CLOCKWISE_90", "reference_location": {"x": 50, "y": 50, "z": 20}, "variables": {}}, {"name": "incls", "file_path": "test_include.json", "rotation": "ROTATE_180", "reference_location": {"x": 50, "y": 50, "z": 20}, "variables": {}}, {"name": "incl2", "file_path": "test_include.json", "rotation": "ROTATE_COUNTER_CLOCKWISE_90", "reference_location": {"x": 50, "y": 50, "z": 20}, "variables": {}}]}