from typing import Optional
import FreeCAD  # type: ignore
from FreeCAD import Vector  # type: ignore
import Part  # type: ignore
import sys
import os

sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
from base import Dimension, Side, Point  # type: ignore  # noqa: E402
from surface import Surface  # type: ignore  # noqa: E402
from board import LocatedBoard, BandingType  # type: ignore  # noqa: E402


def get_color_for_banding(banding_type: BandingType) -> tuple[float, float, float, float]:
    if banding_type == BandingType.NONE:  # return gray color
        return (0.4, 0.1, 0.1, 0.0)
    elif banding_type == BandingType.THIN:  # return dirty white
        return (1.0, 1.0, 0.5, 0.0)
    elif banding_type == BandingType.THICK:  # return snow white
        return (1.0, 1.0, 1.0, 0.0)
    elif banding_type == BandingType.REGULAR:  # return almost snow white
        return (0.9, 0.9, 0.9, 0.0)
    elif banding_type == BandingType.SPECIAL:  # return snow white
        return (1.0, 1.0, 1.0, 0.0)
    else:
        raise ValueError("Invalid banding type")


def from_face(face: Part.Face) -> Optional[Surface]:
    surf = face.Surface
    if surf.__class__.__name__ != "Plane":
        return None  # pomijamy np. cylindryczne

    n = surf.Axis  # normalna jako wektor

    # Pobierz granice powierzchni
    u_min, u_max, v_min, v_max = face.ParameterRange

    # Utwórz punkty narożne powierzchni
    corner1 = surf.value(u_min, v_min)
    corner2 = surf.value(u_max, v_max)

    start_point = Point(corner1.x, corner1.y, corner1.z)
    end_point = Point(corner2.x, corner2.y, corner2.z)

    if abs(n.getAngle(Vector(1, 0, 0))) < 1e-6 or abs(n.getAngle(Vector(-1, 0, 0))) < 1e-6:
        return Surface(start_point, end_point, Dimension.X, 0.0)
    elif abs(n.getAngle(Vector(0, 1, 0))) < 1e-6 or abs(n.getAngle(Vector(0, -1, 0))) < 1e-6:
        return Surface(start_point, end_point, Dimension.Y, 0.0)
    elif abs(n.getAngle(Vector(0, 0, 1))) < 1e-6 or abs(n.getAngle(Vector(0, 0, -1))) < 1e-6:
        return Surface(start_point, end_point, Dimension.Z, 0.0)
    else:
        return None  # płaszczyzna skośna – pomijamy


class BoardCad:
    """Placeholder BoardCad class for testing dialog functionality"""
    def __init__(self, located_board: LocatedBoard):
        self.__located_board = located_board
        self.__doc_object = None

    def get_document_object(self):
        return self.__doc_object

    def instantiate_in_freecad(self):
        if self.__doc_object is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part  # type: ignore

        box = self.__located_board.get_shape()
        shape = Part.makeBox(box.x_max - box.x_min, box.y_max - box.y_min, box.z_max - box.z_min)
        shape.translate(FreeCAD.Vector(box.x_min, box.y_min, box.z_min))

        obj = doc.addObject("Part::Feature", self.__located_board.get_name())
        obj.Shape = shape
        for groove_box in self.__located_board.get_groove_boxes():
            groove_shape = Part.makeBox(groove_box.x_max - groove_box.x_min,
                                        groove_box.y_max - groove_box.y_min,
                                        groove_box.z_max - groove_box.z_min)
            groove_shape.translate(FreeCAD.Vector(groove_box.x_min, groove_box.y_min, groove_box.z_min))
            obj.Shape = obj.Shape.cut(groove_shape)
        doc.recompute()
        obj.Label = self.__located_board.get_name()
        # temporarily show all holes as simple cylinders
        for hole in self.__located_board.get_hole_cylinders():
            hole_shape = Part.makeCylinder(hole.radius, hole.height)
            hole_shape.translate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z))
            if hole.get_axis() == Dimension.X:
                hole_shape.rotate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z),
                                  FreeCAD.Vector(0, 1, 0),
                                  90)
            elif hole.get_axis() == Dimension.Y:
                hole_shape.rotate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z),
                                  FreeCAD.Vector(-1, 0, 0),
                                  90)
            obj.Shape = obj.Shape.cut(hole_shape)
            doc.recompute()

        colors = []
        for face in obj.Shape.Faces:
            pl = from_face(face)
            color_assigned = False
            if pl is not None:
                for side in Side:
                    plane, banding_type = self.__located_board.get_side(side)
                    if pl.is_the_same_plane(plane):
                        colors.append(get_color_for_banding(banding_type))
                        color_assigned = True
                        break
            if not color_assigned:
                colors.append(get_color_for_banding(BandingType.NONE))
                pass

        obj.ViewObject.DiffuseColor = colors
        self.__doc_object = obj
