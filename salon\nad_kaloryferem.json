{"$schema": "../cabinet_schema.json", "required_variables": [], "variables": {"szerokosc": 1000, "wysokosc": "2715-100-1530-3", "glebokosc": "340-18-3", "wysokosc_wspornika": 250, "szerokosc_wewnetrzna": "szerokosc-2*18", "wysokosc_polki": "round((wysokosc-2*18)/3)", "szczelina": 2, "szerokosc_drzwi": "(szerokosc-szczelina*3)/2"}, "boards": [{"name": "gora", "width": "szerokosc_wewnetrzna", "height": "glebokosc", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": 0, "z": "wysokosc-18"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "THICK"}, {"name": "dol", "width": "szerokosc_wewnetrzna", "height": "glebokosc", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": 0, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "THICK"}, {"name": "lewy", "width": "glebokosc", "height": "wysokos<PERSON>", "thickness": 18, "orientation": "SIDE", "location": {"x": 0, "y": 0, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "prawy", "width": "glebokosc", "height": "wysokos<PERSON>", "thickness": 18, "orientation": "SIDE", "location": {"x": "szerokosc-18", "y": 0, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "NONE"}, {"name": "wspornik_gorny", "width": "szerokosc_wewnetrzna", "height": "wysokosc_wspornika", "thickness": 18, "orientation": "FRONT", "location": {"x": 18, "y": "glebokosc-18", "z": "wysokosc-18-wysokosc_wspornika"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "plecy", "width": "szerokosc_wewnetrzna+7", "height": "wysokosc-wysokosc_wspornika-18+5", "thickness": 3, "orientation": "FRONT", "location": {"x": "18-4", "y": "glebokosc-3", "z": "13"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "polka1", "width": "szerokosc_wewnetrzna", "height": "glebokosc-3-10", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": "10", "z": "wysokosc_polki"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "polka2", "width": "szerokosc_wewnetrzna", "height": "glebokosc-3-10", "thickness": 18, "orientation": "FLAT", "location": {"x": 18, "y": "10", "z": "wysokosc_polki*2"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "THICK", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "drzwi_lewe", "width": "szerokosc_drzwi", "height": "wysokosc+3-<PERSON><PERSON><PERSON><PERSON>a", "thickness": 18, "orientation": "FRONT", "location": {"x": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "y": "-18", "z": "-3"}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}, {"name": "drzwi_prawe", "width": "szerokosc_drzwi", "height": "wysokosc+3-<PERSON><PERSON><PERSON><PERSON>a", "thickness": 18, "orientation": "FRONT", "location": {"x": "szerokosc_drzwi+szczelina*2", "y": "-18", "z": "-3"}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}], "L_joins": [{"name": "dowels_1", "joint_type": "cam_lock", "cam_lock_type": "left_up", "board_edge_name": "prawy", "board_face_name": "gora"}, {"name": "dowels_2", "joint_type": "cam_lock", "cam_lock_type": "down_back", "board_edge_name": "wspornik_gorny", "board_face_name": "gora"}, {"name": "dowels_3", "joint_type": "cam_lock", "cam_lock_type": "right_back", "board_edge_name": "wspornik_gorny", "board_face_name": "lewy"}, {"name": "dowels_4", "joint_type": "cam_lock", "cam_lock_type": "right_up", "board_edge_name": "lewy", "board_face_name": "gora"}, {"name": "dowels_6", "joint_type": "cam_lock", "cam_lock_type": "left_front", "board_edge_name": "wspornik_gorny", "board_face_name": "prawy"}, {"name": "dowels_7", "joint_type": "cam_lock", "cam_lock_type": "left_up", "board_edge_name": "polka1", "board_face_name": "prawy"}, {"name": "dowels_8", "joint_type": "cam_lock", "cam_lock_type": "left_down", "board_edge_name": "polka2", "board_face_name": "prawy"}, {"name": "dowels_9", "joint_type": "cam_lock", "cam_lock_type": "right_up", "board_edge_name": "polka1", "board_face_name": "lewy"}, {"name": "dowels_10", "joint_type": "cam_lock", "cam_lock_type": "right_down", "board_edge_name": "polka2", "board_face_name": "lewy"}]}