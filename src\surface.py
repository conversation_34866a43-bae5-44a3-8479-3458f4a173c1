from base import Dimension, Rotation, Point
from dataclasses import dataclass


@dataclass
class Surface:
    start_point: Point
    end_point: Point
    normal: Dimension
    depth: float

    def __post_init__(self):
        if self.normal == Dimension.X:
            if self.start_point.x != self.end_point.x:
                raise ValueError("Start and end point must have the same x coordinate, " +
                                 f"{self.normal}: {self.start_point} != {self.end_point}")
            if self.start_point.y == self.end_point.y:
                raise ValueError("Start and end point must have different y coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")
            if self.start_point.z == self.end_point.z:
                raise ValueError("Start and end point must have different z coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")
        elif self.normal == Dimension.Y:
            if self.start_point.y != self.end_point.y:
                raise ValueError("Start and end point must have the same y coordinate, " +
                                 f"{self.normal}: {self.start_point} != {self.end_point}")
            if self.start_point.x == self.end_point.x:
                raise ValueError("Start and end point must have different x coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")
            if self.start_point.z == self.end_point.z:
                raise ValueError("Start and end point must have different z coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")
        elif self.normal == Dimension.Z:
            if self.start_point.z != self.end_point.z:
                raise ValueError("Start and end point must have the same z coordinate, " +
                                 f"{self.normal}: {self.start_point} != {self.end_point}")
            if self.start_point.x == self.end_point.x:
                raise ValueError("Start and end point must have different x coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")
            if self.start_point.y == self.end_point.y:
                raise ValueError("Start and end point must have different y coordinates, " +
                                 f"{self.normal}: {self.start_point} == {self.end_point}")

    def offset(self) -> float:
        if self.normal == Dimension.X:
            return self.start_point.x
        elif self.normal == Dimension.Y:
            return self.start_point.y
        elif self.normal == Dimension.Z:
            return self.start_point.z
        else:
            raise ValueError(f"Invalid normal: {self.normal}")

    def is_the_same_plane(self, other: "Surface") -> bool:
        return self.normal == other.normal and self.offset() == other.offset()

    def overlaps_with_surface(self, other: "Surface") -> bool:
        try:
            self.get_common_surface(other)
            return True
        except ValueError:
            return False

    def get_common_surface(self, other: "Surface") -> "Surface":
        if not self.is_the_same_plane(other):
            raise ValueError("Planes are not equal")
        new_start = Point(max(min(self.start_point.x, self.end_point.x),
                              min(other.start_point.x, other.end_point.x)),
                          max(min(self.start_point.y, self.end_point.y),
                              min(other.start_point.y, other.end_point.y)),
                          max(min(self.start_point.z, self.end_point.z),
                              min(other.start_point.z, other.end_point.z)))
        new_end = Point(min(max(self.start_point.x, self.end_point.x), max(other.start_point.x, other.end_point.x)),
                        min(max(self.start_point.y, self.end_point.y), max(other.start_point.y, other.end_point.y)),
                        min(max(self.start_point.z, self.end_point.z), max(other.start_point.z, other.end_point.z)))
        return Surface(new_start, new_end, self.normal, 0)

    def rotate_and_shift(self, rotation: Rotation, shift: Point) -> "Surface":
        new_depth = self.depth
        if rotation == Rotation.ROTATE_CLOCKWISE_90 and self.normal == Dimension.X:
            new_depth = -self.depth
        elif rotation == Rotation.ROTATE_COUNTER_CLOCKWISE_90 and self.normal == Dimension.Y:
            new_depth = -self.depth
        elif rotation == Rotation.ROTATE_180 and (self.normal == Dimension.X or self.normal == Dimension.Y):
            new_depth = -self.depth
        return Surface(self.start_point.rotate(rotation)+shift,
                       self.end_point.rotate(rotation)+shift,
                       self.normal.rotate_along_z(rotation),
                       new_depth)

    def get_surface_length(self) -> tuple[int, Dimension]:
        length_y = self.end_point.y - self.start_point.y
        length_z = self.end_point.z - self.start_point.z
        length_x = self.end_point.x - self.start_point.x
        if self.normal == Dimension.X:
            if length_y > length_z:
                return round(length_y), Dimension.Y
            else:
                return round(length_z), Dimension.Z
        elif self.normal == Dimension.Y:
            if length_x > length_z:
                return round(length_x), Dimension.X
            else:
                return round(length_z), Dimension.Z
        elif self.normal == Dimension.Z:
            if length_x > length_y:
                return round(length_x), Dimension.X
            else:
                return round(length_y), Dimension.Y
        else:
            raise ValueError(f"Invalid normal: {self.normal}")

    def get_surface_width(self) -> tuple[int, Dimension]:
        length_y = abs(self.end_point.y - self.start_point.y)
        length_z = abs(self.end_point.z - self.start_point.z)
        length_x = abs(self.end_point.x - self.start_point.x)
        if self.normal == Dimension.X:
            if length_y < length_z:
                return round(length_y), Dimension.Y
            else:
                return round(length_z), Dimension.Z
        elif self.normal == Dimension.Y:
            if length_x < length_z:
                return round(length_x), Dimension.X
            else:
                return round(length_z), Dimension.Z
        elif self.normal == Dimension.Z:
            if length_x < length_y:
                return round(length_x), Dimension.X
            else:
                return round(length_y), Dimension.Y
        else:
            raise ValueError(f"Invalid normal: {self.normal}")
