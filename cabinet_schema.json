{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://example.com/cabinet.schema.json", "title": "Cabinet <PERSON><PERSON>a", "type": "object", "properties": {"$schema": {"type": "string"}, "boards": {"type": "array", "items": {"$ref": "#/definitions/board"}}, "dowels": {"type": "array", "items": {"$ref": "#/definitions/dowel"}}, "complex_spacers": {"type": "array", "items": {"$ref": "#/definitions/complex_spacer"}}, "include": {"type": "array", "items": {"$ref": "#/definitions/include"}}, "variables": {"type": "object", "additionalProperties": {"type": ["string", "number"]}}, "required_variables": {"type": "array", "items": {"type": "string"}}, "L_joins": {"type": "array", "items": {"$ref": "#/definitions/L_join"}}}, "additionalProperties": false, "definitions": {"board": {"type": "object", "properties": {"name": {"type": "string"}, "width": {"type": ["string", "number"]}, "height": {"type": ["string", "number"]}, "thickness": {"type": ["string", "number"]}, "orientation": {"type": "string", "enum": ["FLAT", "SIDE", "FRONT"]}, "location": {"$ref": "#/definitions/location"}, "holes": {"type": "array", "items": {"$ref": "#/definitions/hole"}}, "grooves": {"type": "array", "items": {"$ref": "#/definitions/groove"}}, "banding_top": {"type": "string", "enum": ["NONE", "THIN", "THICK", "REGULAR", "SPECIAL"]}, "banding_bottom": {"type": "string", "enum": ["NONE", "THIN", "THICK", "REGULAR", "SPECIAL"]}, "banding_left": {"type": "string", "enum": ["NONE", "THIN", "THICK", "REGULAR", "SPECIAL"]}, "banding_right": {"type": "string", "enum": ["NONE", "THIN", "THICK", "REGULAR", "SPECIAL"]}}, "required": ["name", "width", "height", "orientation", "location"], "additionalProperties": false}, "dowel": {"type": "object", "properties": {"name": {"type": "string"}, "length": {"type": ["string", "number"]}, "diameter": {"type": ["string", "number"]}, "orientation": {"type": "string", "enum": ["FLAT", "SIDE", "FRONT"]}, "location": {"$ref": "#/definitions/location"}}, "required": ["name", "length", "diameter", "orientation", "location"], "additionalProperties": false}, "complex_spacer": {"type": "object", "properties": {"name": {"type": "string"}, "location": {"$ref": "#/definitions/location"}, "boxes": {"type": "array", "items": {"$ref": "#/definitions/box"}}, "cylinders": {"type": "array", "items": {"$ref": "#/definitions/cylinder"}}}, "required": ["name", "location"], "additionalProperties": false}, "include": {"type": "object", "properties": {"name": {"type": "string"}, "file_path": {"type": "string"}, "rotation": {"type": "string", "enum": ["NONE", "ROTATE_CLOCKWISE_90", "ROTATE_180", "ROTATE_COUNTER_CLOCKWISE_90"]}, "reference_location": {"$ref": "#/definitions/location"}, "variables": {"type": "object", "additionalProperties": {"type": ["string", "number"]}}}, "required": ["name", "file_path", "rotation", "reference_location"], "additionalProperties": false}, "hole": {"type": "object", "properties": {"type": {"type": "string", "enum": ["BackHole", "FrontHole", "SideHole", "LeftHole", "RightHole", "TopHole", "BottomHole"]}, "diameter": {"type": ["string", "number"]}, "depth": {"type": ["string", "number"]}, "position_from_left": {"type": ["string", "number"]}, "position_from_bottom": {"type": ["string", "number"]}}, "required": ["type", "diameter", "depth"], "additionalProperties": false}, "groove": {"type": "object", "properties": {"face": {"type": "string", "enum": ["FRONT", "BACK", "LEFT", "RIGHT"]}, "edge": {"type": "string", "enum": ["TOP", "BOTTOM", "LEFT", "RIGHT"]}, "depth": {"type": ["string", "number"]}, "width": {"type": ["string", "number"]}, "distance_from_edge": {"type": ["string", "number"]}, "distance_1_if_not_through": {"type": ["string", "number"]}, "distance_2_if_not_through": {"type": ["string", "number"]}}, "required": ["face", "edge", "depth", "width", "distance_from_edge"], "additionalProperties": false}, "location": {"type": "object", "properties": {"x": {"type": ["string", "number"]}, "y": {"type": ["string", "number"]}, "z": {"type": ["string", "number"]}}, "required": ["x", "y", "z"], "additionalProperties": false}, "box": {"type": "object", "properties": {"x_min": {"type": ["string", "number"]}, "x_max": {"type": ["string", "number"]}, "y_min": {"type": ["string", "number"]}, "y_max": {"type": ["string", "number"]}, "z_min": {"type": ["string", "number"]}, "z_max": {"type": ["string", "number"]}}, "required": ["x_min", "x_max", "y_min", "y_max", "z_min", "z_max"], "additionalProperties": false}, "cylinder": {"type": "object", "properties": {"radius": {"type": ["string", "number"]}, "height": {"type": ["string", "number"]}, "center": {"$ref": "#/definitions/location"}, "axis": {"type": "string", "enum": ["X", "Y", "Z"]}}, "required": ["radius", "height", "center", "axis"], "additionalProperties": false}, "L_join": {"type": "object", "properties": {"name": {"type": "string"}, "joint_type": {"type": "string", "enum": ["dowel", "confirmat_7x50", "cam_lock"]}, "cam_lock_type": {"type": "string", "enum": ["right_down", "right_up", "right_front", "right_back", "down_front", "down_back", "left_down", "left_up", "left_front", "left_back"]}, "board_edge_name": {"type": "string"}, "board_face_name": {"type": "string"}}, "required": ["name", "board_edge_name", "board_face_name"], "additionalProperties": false}}}