from cabinet import Cabinet
from board import Board, BandingType, LocatedBoard, FlatLocatedBoard, FrontLocatedBoard, SideLocatedBoard
from board_hole import Hole, FrontHole, BackHole, LeftHole, RightHole, TopHole, BottomHole
from board_groove import Groove, GrooveDepth, GrooveWidth, GrooveDistanceFromEdge
from base import Orientation, Point, Rotation, Dimension, Side
from cylinder import Cylinder
from board_join_L import BoardJoin_L_dowel, BoardJoin_L_confirmat_7x50, BoardJoin_L_cam_lock, BoardJoin_L
from box import Box
from dowel import Dowel, LocatedDowel, ZLocatedDowel, XLocatedDowel, YLocatedDowel
from complex_spacer import ComplexSpacer, ComplexSpacerLocated
import sys
import os
from typing import Any, Optional


class Evaluator:
    variables: dict[str, float]

    def __init__(self):
        self.variables = {}

    safe_functions = {
        'abs': abs, 'min': min, 'max': max,
        'round': round, 'float': float,
        'pow': pow, 'sqrt': lambda x: x**0.5
    }

    def is_empty(self) -> bool:
        return len(self.variables) == 0

    def add_variable(self, name: str, value: Any) -> bool:
        if name in self.variables:
            return False
        resolved_value = self.evaluate(value)
        self.variables[name] = resolved_value
        return True

    def evaluate(self, value: Any) -> float:
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            for var_name, var_value in [(var_name, var_value)
                                        for var_name, var_value in sorted(self.variables.items(),
                                                                          key=lambda item: len(item[0]),
                                                                          reverse=True)]:
                value = value.replace(var_name, str(var_value))
            try:
                return float(eval(value, self.safe_functions))
            except Exception as e:
                raise ValueError(f"Cannot evaluate value {value}: {e}")
        else:
            raise ValueError(f"Invalid value: {value}")

    @staticmethod
    def half_float_validator(value: float) -> float:
        if value % 0.5 == 0:
            return value
        else:
            raise ValueError(f"Value is not multiple of 0.5: {value}")


class CabinetLoader:
    def load_from_file(self, file_path: str, evaluator: Optional[Evaluator] = None, rotation: Rotation = Rotation.NONE,
                       reference_location: Point = Point(0, 0, 0), name_prefix: str = "") -> Cabinet:
        if evaluator is None:
            evaluator = Evaluator()
        import json
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        if not evaluator.is_empty():
            for var_name in data.get('required_variables', []):
                if var_name not in evaluator.variables:
                    raise ValueError(f"Missing required variable: {var_name}")
        for var_name, var_value in data.get('variables', {}).items():
            evaluator.add_variable(var_name, var_value)
        cabinet = Cabinet()
        if 'boards' in data:
            for board_data in data['boards']:
                board = self._create_board_from_data(name_prefix, board_data, evaluator, rotation, reference_location)
                cabinet.add_board(board)
        if 'dowels' in data:
            for dowel_data in data['dowels']:
                dowel = self._create_dowel_from_data(name_prefix, dowel_data, evaluator, rotation, reference_location)
                cabinet.add_dowel(dowel)
        if 'complex_spacers' in data:
            for complex_spacer_data in data['complex_spacers']:
                complex_spacer: ComplexSpacerLocated = self._create_complex_spacer_from_data(name_prefix,
                                                                                             complex_spacer_data,
                                                                                             evaluator,
                                                                                             rotation,
                                                                                             reference_location)
                cabinet.add_complex_spacer(complex_spacer)
        if 'include' in data:
            for include_data in data['include']:
                local_variables = Evaluator()
                local_name_prefix: str = name_prefix + include_data['name'] + '.'
                for var_name, var_value in data.get('variables', {}).items():
                    local_variables.add_variable(var_name, evaluator.evaluate(var_value))
                local_file_path = include_data.get('file_path')
                full_file_path = os.path.join(os.path.dirname(file_path), local_file_path)
                include_rotation = Rotation[include_data.get('rotation', 'NONE')]
                include_reference_location = Point(
                    x=evaluator.evaluate(include_data.get('reference_location', {}).get('x', 0)),
                    y=evaluator.evaluate(include_data.get('reference_location', {}).get('y', 0)),
                    z=evaluator.evaluate(include_data.get('reference_location', {}).get('z', 0))
                )
                include_rotation = rotation.combine_rotation(include_rotation)
                include_reference_location = include_reference_location.rotate(rotation) + reference_location
                loader = CabinetLoader()
                included_cabinet = loader.load_from_file(full_file_path, local_variables,
                                                         include_rotation, include_reference_location,
                                                         local_name_prefix)
                for board in included_cabinet.boards:
                    cabinet.add_board(board)
                for dowel in included_cabinet.dowels:
                    cabinet.add_dowel(dowel)
                for complex_spacer in included_cabinet.complex_spacers:
                    cabinet.add_complex_spacer(complex_spacer)
        if 'L_joins' in data:
            for L_join_data in data['L_joins']:
                L_join = self._create_L_join_from_data(cabinet, name_prefix, L_join_data)
                for dowel in L_join.dowels:
                    cabinet.add_dowel(dowel)
                for complex_spacer in L_join.complex_spacers:
                    complex_spacer.reference_rotation = rotation.combine_rotation(complex_spacer.reference_rotation)
                    cabinet.add_complex_spacer(complex_spacer)
        return cabinet

    @staticmethod
    def _get_hole_from_data(hole_data: dict, evaluator: Evaluator) -> Hole:
        diameter = int(evaluator.evaluate(hole_data['diameter']))
        depth = int(evaluator.evaluate(hole_data['depth']))
        if hole_data['type'] == 'BackHole':
            return BackHole(
                diameter,
                depth,
                position_from_left=int(evaluator.evaluate(hole_data['position_from_left'])),
                position_from_bottom=int(evaluator.evaluate(hole_data['position_from_bottom']))
            )
        elif hole_data['type'] == 'FrontHole':
            return FrontHole(
                diameter,
                depth,
                position_from_left=int(evaluator.evaluate(hole_data['position_from_left'])),
                position_from_bottom=int(evaluator.evaluate(hole_data['position_from_bottom']))
            )
        elif hole_data['type'] == 'LeftHole':
            return LeftHole(
                diameter,
                depth,
                position_from_bottom=int(evaluator.evaluate(hole_data['position_from_bottom']))
            )
        elif hole_data['type'] == 'RightHole':
            return RightHole(
                diameter,
                depth,
                position_from_bottom=int(evaluator.evaluate(hole_data['position_from_bottom']))
            )
        elif hole_data['type'] == 'TopHole':
            return TopHole(
                diameter,
                depth,
                position_from_left=int(evaluator.evaluate(hole_data['position_from_left']))
            )
        elif hole_data['type'] == 'BottomHole':
            return BottomHole(
                diameter,
                depth,
                position_from_left=int(evaluator.evaluate(hole_data['position_from_left']))
            )
        else:
            raise ValueError(f"Invalid hole type: {hole_data['type']}")

    @staticmethod
    def _get_groove_from_data(groove_data: dict, evaluator: Evaluator) -> Groove:
        depth = int(evaluator.evaluate(groove_data['depth']))
        try:
            depth = GrooveDepth(depth)
        except KeyError:
            raise ValueError(f"Invalid groove depth: {depth} in {groove_data['depth']}")
        width = evaluator.evaluate(groove_data['width'])
        try:
            width = GrooveWidth(width)
        except KeyError:
            raise ValueError(f"Invalid groove width: {width} in {groove_data['width']}")
        return Groove(
            face=Side[groove_data['face']],
            edge=Side[groove_data['edge']],
            depth=depth,
            width=width,
            distance_from_edge=GrooveDistanceFromEdge(int(evaluator.evaluate(groove_data['distance_from_edge']))),
            distance_1_if_not_through=int(evaluator.evaluate(groove_data.get('distance_1_if_not_through', -1))),
            distance_2_if_not_through=int(evaluator.evaluate(groove_data.get('distance_2_if_not_through', -1)))
        )

    def _create_board_from_data(self, name_prefix: str, board_data: dict, evaluator: Evaluator, rotation: Rotation,
                                reference_location: Point) -> LocatedBoard:
        holes: list[Hole] = []
        for hole_data in board_data.get('holes', []):
            holes.append(self._get_hole_from_data(hole_data, evaluator))
        grooves: list[Groove] = []
        for groove_data in board_data.get('grooves', []):
            grooves.append(self._get_groove_from_data(groove_data, evaluator))
        board = Board(
            name=name_prefix+board_data['name'],
            width=int(evaluator.evaluate(board_data['width'])),
            height=int(evaluator.evaluate(board_data['height'])),
            thickness=int(evaluator.evaluate(board_data.get('thickness', 18))),
            holes=holes,
            grooves=grooves,
            banding_top=BandingType[board_data.get('banding_top', 'NONE')],
            banding_bottom=BandingType[board_data.get('banding_bottom', 'NONE')],
            banding_left=BandingType[board_data.get('banding_left', 'NONE')],
            banding_right=BandingType[board_data.get('banding_right', 'NONE')]
        )
        orientation = Orientation[board_data['orientation']]
        location_data = board_data['location']
        location = Point(
            x=Evaluator.half_float_validator(evaluator.evaluate(location_data['x'])),
            y=Evaluator.half_float_validator(evaluator.evaluate(location_data['y'])),
            z=Evaluator.half_float_validator(evaluator.evaluate(location_data['z']))
        )
        if orientation == Orientation.FRONT:
            return FrontLocatedBoard(board, location, rotation, reference_location)
        elif orientation == Orientation.SIDE:
            return SideLocatedBoard(board, location, rotation, reference_location)
        elif orientation == Orientation.FLAT:
            return FlatLocatedBoard(board, location, rotation, reference_location)
        else:
            raise ValueError(f"Invalid board orientation: {orientation}")

    def _create_dowel_from_data(self, name_prefix: str, dowel_data: dict, evaluator: Evaluator, rotation: Rotation,
                                reference_location: Point) -> LocatedDowel:
        name = name_prefix+dowel_data['name']
        length = int(evaluator.evaluate(dowel_data.get('length', 35)))
        diameter = int(evaluator.evaluate(dowel_data.get('diameter', 8)))
        orientation = Orientation[dowel_data['orientation']]
        location_data = dowel_data['location']
        location = Point(
            x=Evaluator.half_float_validator(evaluator.evaluate(location_data['x'])),
            y=Evaluator.half_float_validator(evaluator.evaluate(location_data['y'])),
            z=Evaluator.half_float_validator(evaluator.evaluate(location_data['z']))
        )
        dowel = Dowel(name, length, diameter)
        if orientation == Orientation.FRONT:
            return ZLocatedDowel(dowel, location, rotation, reference_location)
        elif orientation == Orientation.SIDE:
            return XLocatedDowel(dowel, location, rotation, reference_location)
        elif orientation == Orientation.FLAT:
            return YLocatedDowel(dowel, location, rotation, reference_location)
        else:
            raise ValueError(f"Invalid dowel orientation: {orientation}")

    def _create_complex_spacer_from_data(self, name_prefix: str, complex_spacer_data: dict, evaluator: Evaluator,
                                         rotation: Rotation, reference_location: Point) -> ComplexSpacerLocated:
        name = name_prefix+complex_spacer_data['name']
        location_data = complex_spacer_data['location']
        location = Point(
            x=Evaluator.half_float_validator(evaluator.evaluate(location_data['x'])),
            y=Evaluator.half_float_validator(evaluator.evaluate(location_data['y'])),
            z=Evaluator.half_float_validator(evaluator.evaluate(location_data['z']))
        )
        boxes = []
        for box_data in complex_spacer_data.get('boxes', []):
            boxes.append(self._get_box_from_data(box_data, evaluator))
        cylinders = []
        for cylinder_data in complex_spacer_data.get('cylinders', []):
            cylinders.append(self._get_cylinder_from_data(cylinder_data, evaluator))
        complex_spacer = ComplexSpacer(name, cylinders, boxes)
        return ComplexSpacerLocated(complex_spacer, location, rotation, reference_location)

    def _get_box_from_data(self, box_data: dict, evaluator: Evaluator) -> Box:
        return Box(
            x_min=Evaluator.half_float_validator(evaluator.evaluate(box_data['x_min'])),
            x_max=Evaluator.half_float_validator(evaluator.evaluate(box_data['x_max'])),
            y_min=Evaluator.half_float_validator(evaluator.evaluate(box_data['y_min'])),
            y_max=Evaluator.half_float_validator(evaluator.evaluate(box_data['y_max'])),
            z_min=Evaluator.half_float_validator(evaluator.evaluate(box_data['z_min'])),
            z_max=Evaluator.half_float_validator(evaluator.evaluate(box_data['z_max']))
        )

    def _get_cylinder_from_data(self, cylinder_data: dict, evaluator: Evaluator) -> Cylinder:
        radius = Evaluator.half_float_validator(evaluator.evaluate(cylinder_data['radius']))
        height = Evaluator.half_float_validator(evaluator.evaluate(cylinder_data['height']))
        center_data = cylinder_data['center']
        center = Point(
            x=Evaluator.half_float_validator(evaluator.evaluate(center_data['x'])),
            y=Evaluator.half_float_validator(evaluator.evaluate(center_data['y'])),
            z=Evaluator.half_float_validator(evaluator.evaluate(center_data['z']))
        )
        return Cylinder.cylinder_factory(radius, height, center, Dimension[cylinder_data['axis']])

    def _create_L_join_from_data(self, cabinet: Cabinet, name_prefix: str, L_join_data: dict) -> BoardJoin_L:
        name = name_prefix+L_join_data['name']
        board_edge_name = name_prefix+L_join_data['board_edge_name']
        board_face_name = name_prefix+L_join_data['board_face_name']
        board_edge = cabinet.get_board(board_edge_name)
        board_face = cabinet.get_board(board_face_name)
        if L_join_data['joint_type'] == 'dowel':
            return BoardJoin_L_dowel(name, board_edge, board_face)
        elif L_join_data['joint_type'] == 'confirmat_7x50':
            return BoardJoin_L_confirmat_7x50(name, board_edge, board_face)
        elif L_join_data['joint_type'] == 'cam_lock':
            return BoardJoin_L_cam_lock(name, board_edge, board_face, str(L_join_data['cam_lock_type']))
        else:
            raise ValueError(f"Invalid joint type: {L_join_data['joint_type']}")


if __name__ == "__main__":
    cabinet = CabinetLoader().load_from_file(sys.argv[1])
    cabinet.check_colisions()
    with open("board_info.txt", "w", encoding="utf-8") as f:
        for board in cabinet.boards:
            stream = board.board.printout(f)
    print("Board information written to 'board_info.txt'")
