from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass


class Rotation(Enum):
    """Represents the rotation of a physical item."""
    NONE = "none"
    ROTATE_CLOCKWISE_90 = "rotate_clockwise_90"
    ROTATE_COUNTER_CLOCKWISE_90 = "rotate_counter_clockwise_90"
    ROTATE_180 = "rotate_180"

    def combine_rotation(self, other: "Rotation") -> "Rotation":
        if self == Rotation.NONE:
            return other
        elif other == Rotation.NONE:
            return self
        elif self == Rotation.ROTATE_CLOCKWISE_90:
            if other == Rotation.ROTATE_CLOCKWISE_90:
                return Rotation.ROTATE_180
            elif other == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
                return Rotation.NONE
            elif other == Rotation.ROTATE_180:
                return Rotation.ROTATE_COUNTER_CLOCKWISE_90
            else:
                raise ValueError(f"Invalid rotation, {self} and {other}")
        elif self == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            if other == Rotation.ROTATE_CLOCKWISE_90:
                return Rotation.NONE
            elif other == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
                return Rotation.ROTATE_180
            elif other == Rotation.ROTATE_180:
                return Rotation.ROTATE_CLOCKWISE_90
            else:
                raise ValueError(f"Invalid rotation, {self} and {other}")
        elif self == Rotation.ROTATE_180:
            if other == Rotation.ROTATE_CLOCKWISE_90:
                return Rotation.ROTATE_COUNTER_CLOCKWISE_90
            elif other == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
                return Rotation.ROTATE_CLOCKWISE_90
            elif other == Rotation.ROTATE_180:
                return Rotation.NONE
            else:
                raise ValueError(f"Invalid rotation, {self} and {other}")
        else:
            raise ValueError(f"Invalid rotation, {self} and {other}")

    def inverse(self) -> "Rotation":
        if self == Rotation.NONE:
            return Rotation.NONE
        elif self == Rotation.ROTATE_CLOCKWISE_90:
            return Rotation.ROTATE_COUNTER_CLOCKWISE_90
        elif self == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            return Rotation.ROTATE_CLOCKWISE_90
        elif self == Rotation.ROTATE_180:
            return Rotation.ROTATE_180
        else:
            raise ValueError(f"Invalid rotation: {self}")


class Dimension(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"

    def rotate_along_z(self, rotation: Rotation) -> "Dimension":
        if rotation == Rotation.NONE or rotation == Rotation.ROTATE_180:
            return self
        elif rotation == Rotation.ROTATE_CLOCKWISE_90:
            if self == Dimension.X:
                return Dimension.Y
            elif self == Dimension.Y:
                return Dimension.X
            else:
                return self
        elif rotation == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            if self == Dimension.X:
                return Dimension.Y
            elif self == Dimension.Y:
                return Dimension.X
            else:
                return self
        else:
            raise ValueError(f"Invalid rotation: {rotation}")


class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

    def get_primary_dimension(self) -> Dimension:
        if self == Orientation.FLAT:
            return Dimension.Y
        elif self == Orientation.FRONT:
            return Dimension.Z
        elif self == Orientation.SIDE:
            return Dimension.X
        else:
            raise ValueError("Invalid orientation")


@dataclass
class Point:
    """Represents a point in 3D space."""
    x: float
    y: float
    z: float

    @classmethod
    def create_point_from_dimension_values(cls, points: dict[Dimension, float]) -> "Point":
        return cls(
            x=points.get(Dimension.X, 0.0),
            y=points.get(Dimension.Y, 0.0),
            z=points.get(Dimension.Z, 0.0)
        )

    def get_coordinate(self, dimension: Dimension) -> float:
        if dimension == Dimension.X:
            return self.x
        elif dimension == Dimension.Y:
            return self.y
        elif dimension == Dimension.Z:
            return self.z
        else:
            raise ValueError("Invalid dimension")

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

    def rotate(self, rotation: Rotation) -> "Point":
        if rotation == Rotation.ROTATE_CLOCKWISE_90:
            return self.rotate_90_clockwise_along_z()
        elif rotation == Rotation.ROTATE_180:
            return self.rotate_180_clockwise_along_z()
        elif rotation == Rotation.ROTATE_COUNTER_CLOCKWISE_90:
            return self.rotate_270_clockwise_along_z()
        else:
            return self

    def rotate_90_clockwise_along_z(self) -> "Point":
        return Point(self.y,
                     - self.x,
                     self.z)
        # return Point(- rotate_point.x - rotate_point.y + self.y,
        #              rotate_point.y + rotate_point.x - self.x - rotate_point.x,
        #              self.z)

    def rotate_180_clockwise_along_z(self) -> "Point":
        return Point(- self.x,
                     - self.y,
                     self.z)
        # return Point(-rotate_point.x - self.x + rotate_point.x,
        #              rotate_point.y + rotate_point.y - self.y,
        #              self.z)

    def rotate_270_clockwise_along_z(self) -> "Point":
        return Point(- self.y,
                     self.x,
                     self.z)
        # return Point(rotate_point.x + self.y - rotate_point.y,
        #              rotate_point.y + rotate_point.x - self.x,
        #              self.z)


class NamedItem(ABC):
    @abstractmethod
    def get_name(self) -> str:
        pass


class Side(Enum):
    """Represents the side of a board."""
    FRONT = "front"
    BACK = "back"
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"

    def is_face_side(self) -> bool:
        return self in [Side.FRONT, Side.BACK]

    def is_edge_side(self) -> bool:
        return self in [Side.LEFT, Side.RIGHT, Side.TOP, Side.BOTTOM]
