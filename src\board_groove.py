from base import Side
from dataclasses import dataclass


class GrooveDepth(int):
    def __new__(cls, value):
        if value not in [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]:
            raise ValueError("Invalid groove depth " + str(value) + "mm, allowed values: 2-13mm")
        return super().__new__(cls, value)


class GrooveWidth(float):
    def __new__(cls, value):
        if value not in [3.2, 4.2, 10.2]:
            raise ValueError("Invalid groove width " + str(value) + "mm, allowed values: 3.2, 4.2, 10.2mm")
        return super().__new__(cls, value)


class GrooveDistanceFromEdge(int):
    def __new__(cls, value):
        if not 0 <= value <= 50:
            raise ValueError("Groove distance must be in distance 0-50.")
        return super().__new__(cls, value)


@dataclass
class Groove():
    face: Side
    edge: Side
    depth: GrooveDepth
    width: GrooveWidth
    distance_from_edge: GrooveDistanceFromEdge
    distance_1_if_not_through: int
    distance_2_if_not_through: int
