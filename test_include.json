{"$schema": "cabinet_schema.json", "variables": {}, "boards": [{"name": "bzero", "width": 2, "height": 2, "thickness": 10, "orientation": "FLAT", "location": {"x": -1, "y": -1, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "bzero2", "width": 2, "height": 2, "thickness": 10, "orientation": "FLAT", "location": {"x": -1, "y": 3, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "binclude", "width": 20, "height": 60, "thickness": 8, "orientation": "FLAT", "location": {"x": -10, "y": 35, "z": 10}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}], "dowels": [{"name": "include", "length": 30, "diameter": 8, "orientation": "FLAT", "location": {"x": 0, "y": 1, "z": 4}}, {"name": "north", "length": 40, "diameter": 6, "orientation": "FLAT", "location": {"x": 0, "y": 110, "z": 0}}, {"name": "west", "length": 40, "diameter": 6, "orientation": "SIDE", "location": {"x": -50, "y": 100, "z": 0}}, {"name": "east", "length": 40, "diameter": 6, "orientation": "SIDE", "location": {"x": 10, "y": 100, "z": 0}}, {"name": "up", "length": 40, "diameter": 6, "orientation": "FRONT", "location": {"x": 0, "y": 100, "z": 10}}], "complex_spacers": []}