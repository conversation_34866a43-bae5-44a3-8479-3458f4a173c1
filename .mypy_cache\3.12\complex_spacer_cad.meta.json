{"data_mtime": 1762289778, "dep_lines": [1, 5, 6, 7, 1, 1, 2, 4, 10, 11, 12, 13], "dep_prios": [5, 5, 10, 10, 5, 30, 5, 10, 5, 5, 5, 5], "dependencies": ["typing", "abc", "sys", "os", "builtins", "_frozen_importlib"], "hash": "38da032d3c2c4d35c848bfd5e491bc372f8a58c7", "id": "complex_spacer_cad", "ignore_all": true, "interface_hash": "1867a77b2aba437886320daa36e10e35b02887cb", "mtime": 1760791126, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\complex_spacer_cad.py", "plugin_data": null, "size": 2442, "suppressed": ["FreeCAD", "Part", "base", "box", "cylinder", "complex_spacer"], "version_id": "1.15.0"}