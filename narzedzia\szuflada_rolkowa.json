{"$schema": "../cabinet_schema.json", "required_variables": ["width", "height", "depth", "front_width", "front_overlay_bottom", "front_overlay_top"], "variables": {"width": "600-2*36", "front_width": "600+2", "height": 200, "depth": 450, "front_overlay_bottom": 20, "front_overlay_top": 20, "second_screw": "229 + (261 - 229) * round(max(0, min(1, (width - 301) / abs(width - 301 + 0.001))))"}, "boards": [{"name": "dno", "width": "width-2*12.5", "height": "depth-18+3+3", "thickness": 3, "orientation": "FLAT", "location": {"x": 12.5, "y": -3, "z": 0}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "sciana tylowa", "width": "width-2*12.5-2*18", "height": "height+3", "thickness": 18, "orientation": "FRONT", "location": {"x": "12.5+18", "y": "depth-18", "z": -3}, "holes": [], "grooves": [{"face": "FRONT", "edge": "BOTTOM", "depth": 4, "width": 3.2, "distance_from_edge": 3, "distance_1_if_not_through": -1, "distance_2_if_not_through": -1}], "banding_top": "THICK", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "sciana przednia", "width": "front_width", "height": "height+front_overlay_bottom+front_overlay_top", "thickness": 18, "orientation": "FRONT", "location": {"x": "(width-front_width)/2", "y": -18, "z": "-front_overlay_bottom"}, "holes": [], "grooves": [{"face": "BACK", "edge": "BOTTOM", "depth": 4, "width": 3.2, "distance_from_edge": "front_overlay_bottom", "distance_1_if_not_through": "round((front_width-width)/2)", "distance_2_if_not_through": "round((front_width-width)/2)"}], "banding_top": "THICK", "banding_bottom": "THICK", "banding_left": "THICK", "banding_right": "THICK"}, {"name": "sciana lewa", "width": "depth", "height": "height-3", "thickness": 18, "orientation": "SIDE", "location": {"x": 12.5, "y": 0, "z": 3}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "THICK"}, {"name": "sciana prawa", "width": "depth", "height": "height-3", "thickness": 18, "orientation": "SIDE", "location": {"x": "width-12.5-18", "y": 0, "z": 3}, "holes": [], "grooves": [], "banding_top": "THICK", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "THICK"}], "complex_spacers": [{"name": "dystans", "location": {"x": "0", "y": "0", "z": "0"}, "boxes": [{"x_min": 0, "x_max": 12.5, "y_min": 0, "y_max": "depth-18", "z_min": -15, "z_max": 15}, {"x_min": 12.5, "x_max": "12.5+10", "y_min": 0, "y_max": "depth-18", "z_min": -15, "z_max": 0}, {"x_min": "width-12.5", "x_max": "width", "y_min": 0, "y_max": "depth-18", "z_min": -15, "z_max": 15}, {"x_min": "width-12.5-10", "x_max": "width-12.5", "y_min": 0, "y_max": "depth-18", "z_min": -15, "z_max": 0}, {"x_min": "0", "x_max": "width", "y_min": 0, "y_max": "100", "z_min": "height", "z_max": "height+20"}], "cylinders": [{"radius": 1, "height": 5, "center": {"x": "-5", "y": "37", "z": "14"}, "axis": "X"}, {"radius": 1, "height": 5, "center": {"x": "-5", "y": "second_screw", "z": "14"}, "axis": "X"}, {"radius": 1, "height": 5, "center": {"x": "width", "y": "37", "z": "14"}, "axis": "X"}, {"radius": 1, "height": 5, "center": {"x": "width", "y": "second_screw", "z": "14"}, "axis": "X"}]}]}