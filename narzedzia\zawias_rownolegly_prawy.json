{"$schema": "../cabinet_schema.json", "required_variables": [], "variables": {"poz_x": 0, "poz_y": 0, "poz_z": 0}, "complex_spacers": [{"name": "zaw<PERSON>_rownolegly_prawy", "location": {"x": "poz_x", "y": "poz_y", "z": "poz_z"}, "boxes": [{"x_min": -42, "x_max": -1, "y_min": 0, "y_max": 30, "z_min": -31, "z_max": 31}, {"x_min": 0, "x_max": 42, "y_min": 0, "y_max": 30, "z_min": -26, "z_max": 26}], "cylinders": [{"radius": 2.5, "height": 12, "center": {"x": 20, "y": -12, "z": -16}, "axis": "Y"}, {"radius": 2.5, "height": 12, "center": {"x": 20, "y": -12, "z": 16}, "axis": "Y"}, {"radius": "35/2", "height": 12, "center": {"x": "-35/2-7", "y": -12, "z": 0}, "axis": "Y"}]}]}