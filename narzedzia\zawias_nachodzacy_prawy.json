{"$schema": "../cabinet_schema.json", "required_variables": [], "variables": {"poz_x": 0, "poz_y": 0, "poz_z": 0}, "complex_spacers": [{"name": "z<PERSON><PERSON>_nachod<PERSON>y_prawy", "location": {"x": "poz_x", "y": "poz_y", "z": "poz_z"}, "boxes": [{"x_min": -19, "x_max": 0, "y_min": 5, "y_max": 60, "z_min": -26, "z_max": 26}, {"x_min": -42, "x_max": 0, "y_min": 0, "y_max": 4, "z_min": -31, "z_max": 31}], "cylinders": [{"radius": 2.5, "height": 12, "center": {"x": 0, "y": 37, "z": -16}, "axis": "X"}, {"radius": 2.5, "height": 12, "center": {"x": 0, "y": 37, "z": 16}, "axis": "X"}, {"radius": "35/2", "height": 12, "center": {"x": "12-35/2", "y": -12, "z": 0}, "axis": "Y"}]}]}