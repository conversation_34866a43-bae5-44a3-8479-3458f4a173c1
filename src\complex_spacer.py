from dataclasses import dataclass
from base import Point, Rotation, Dimension
from box import Box
from cylinder import <PERSON><PERSON><PERSON>
from dataclasses import field
from board import LocatedBoard
from dowel import LocatedDowel


@dataclass
class ComplexSpacer:
    name: str
    cylinders: list[Cylinder]
    boxes: list[Box]

    def get_name(self) -> str:
        return self.name

    def get_boxes(self) -> list[Box]:
        return self.boxes

    def get_cylinders(self) -> list[Cylinder]:
        return self.cylinders


@dataclass
class ComplexSpacerLocated():
    complex_spacer: ComplexSpacer
    location: Point
    reference_rotation: Rotation = field(default_factory=lambda: Rotation.NONE)
    reference_location: Point = field(default_factory=lambda: Point(0, 0, 0))

    def get_boxes(self) -> list[Box]:
        return [(box-self.location).rotate(self.reference_location, self.reference_rotation)+self.location
                for box in self.complex_spacer.get_boxes()]

    def get_cylinders(self) -> list[Cylinder]:
        return [(cylinder-self.location).rotate(self.reference_location, self.reference_rotation)+self.location
                for cylinder in self.complex_spacer.get_cylinders()]

    def get_name(self) -> str:
        return self.complex_spacer.get_name()

    def colides_with_board(self, board: LocatedBoard) -> bool:
        def colides_with_boxes(box: Box, compared_board: LocatedBoard) -> bool:
            compared_box = compared_board.get_shape()
            intersection = compared_box.intersection_with_box(box)
            if intersection.is_empty():
                return False
            for gap_box in compared_board.get_groove_boxes():
                if gap_box.fully_contains(intersection):
                    return False
            return True
        if any(colides_with_boxes(box, board) for box in self.get_boxes()):
            return True

        def colides_with_cylinders(cylinder: Cylinder, compared_board: LocatedBoard) -> bool:
            compared_box = compared_board.get_shape()
            intersection = cylinder.intersection_with_box(compared_box)
            if intersection.is_empty():

                return False
            if any(hole_cylinder.fully_contains(intersection) for hole_cylinder in compared_board.get_hole_cylinders()):
                return False
            return True
        if any(colides_with_cylinders(cylinder, board) for cylinder in self.get_cylinders()):
            return True
        return False

    def colides_with_dowel(self, dowel: LocatedDowel) -> bool:
        dowel_cylinder = dowel.get_cylinder()
        if any(cylinder.overlaps_with_cylinder(dowel_cylinder) for cylinder in self.get_cylinders()):
            return True
        if any(not dowel_cylinder.intersection_with_box(box).is_empty() for box in self.get_boxes()):
            return True
        return False

    @staticmethod
    def confirmat_7x50_down(name: str, location: Point, rotation: Rotation) -> 'ComplexSpacerLocated':
        cylinder = Cylinder.cylinder_factory(4, 18, Point(location.x, location.y, location.z), Dimension.Z)
        cylinder_2 = Cylinder.cylinder_factory(2, 53-18, Point(location.x, location.y, location.z-53+18), Dimension.Z)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation)

    @staticmethod
    def confirmat_7x50_up(name: str, location: Point, rotation: Rotation) -> 'ComplexSpacerLocated':
        cylinder = Cylinder.cylinder_factory(4, 18, Point(location.x, location.y, location.z-18), Dimension.Z)
        cylinder_2 = Cylinder.cylinder_factory(2, 53-18, Point(location.x, location.y, location.z), Dimension.Z)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation)

    @staticmethod
    def confirmat_7x50_front(name: str, location: Point, rotation: Rotation) -> 'ComplexSpacerLocated':
        cylinder = Cylinder.cylinder_factory(4, 18, Point(location.x, location.y, location.z), Dimension.Y)
        cylinder_2 = Cylinder.cylinder_factory(2, 53-18, Point(location.x, location.y-53+18, location.z), Dimension.Y)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation)

    @staticmethod
    def cam_lock_down(name: str, location: Point,
                      rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """      |       """
        """     /|`      """
        """     |||      """
        """      O       """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x, location.y, location.z),
                                             Dimension.Z)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x, location.y, location.z-34),
                                               Dimension.Z)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x, location.y-board_thickness/2, location.z-34),
                                               Dimension.Y)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)

    @staticmethod
    def cam_lock_up(name: str, location: Point,
                    rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """      O       """
        """     |||      """
        """     /|`      """
        """      |       """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x, location.y, location.z-10),
                                             Dimension.Z)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x, location.y, location.z),
                                               Dimension.Z)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x, location.y-board_thickness/2, location.z+34),
                                               Dimension.Y)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)

    @staticmethod
    def cam_lock_right(name: str, location: Point,
                       rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """     ,,,,     """
        """  --<---O     """
        """     ''''     """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x-10, location.y, location.z),
                                             Dimension.X)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x, location.y, location.z),
                                               Dimension.X)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x+34, location.y-board_thickness/2, location.z),
                                               Dimension.Y)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)

    @staticmethod
    def cam_lock_left(name: str, location: Point,
                      rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """     ,,,,     """
        """     O--->--  """
        """     ''''     """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x, location.y, location.z),
                                             Dimension.X)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x-34, location.y, location.z),
                                               Dimension.X)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x-34, location.y-board_thickness/2, location.z),
                                               Dimension.Y)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)

    @staticmethod
    def cam_lock_right_down(name: str, location: Point,
                            rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """     ,,,,     """
        """  --<---_     """
        """     '''=     """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x-10, location.y, location.z),
                                             Dimension.X)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x, location.y, location.z),
                                               Dimension.X)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x+34, location.y, location.z-board_thickness/2),
                                               Dimension.Z)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)

    @staticmethod
    def cam_lock_right_up(name: str, location: Point,
                          rotation_from_front_lock: Rotation, board_thickness: int) -> 'ComplexSpacerLocated':
        """     ,,,=     """
        """  --<---^     """
        """     ''''     """
        if board_thickness != 18:
            raise ValueError("Board thickness can only be 18mm for cam lock")
        cylinder = Cylinder.cylinder_factory(2.5, 10,
                                             Point(location.x-10, location.y, location.z),
                                             Dimension.X)
        cylinder_2 = Cylinder.cylinder_factory(4, 34,
                                               Point(location.x, location.y, location.z),
                                               Dimension.X)
        cylinder_3 = Cylinder.cylinder_factory(7.5, 13,
                                               Point(location.x+34, location.y, location.z-4),
                                               Dimension.Z)
        complex_spacer = ComplexSpacer(name, [cylinder, cylinder_2, cylinder_3], [])
        return ComplexSpacerLocated(complex_spacer, location, rotation_from_front_lock)
