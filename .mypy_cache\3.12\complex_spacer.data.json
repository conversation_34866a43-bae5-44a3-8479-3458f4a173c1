{".class": "MypyFile", "_fullname": "complex_spacer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Box": {".class": "SymbolTableNode", "cross_ref": "box.Box", "kind": "Gdef"}, "ComplexSpacer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "complex_spacer.ComplexSpacer", "name": "ComplexSpacer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 12, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 13, "name": "cylinders", "type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 14, "name": "boxes", "type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "complex_spacer", "mro": ["complex_spacer.ComplexSpacer", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "complex_spacer.ComplexSpacer.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "cylinders", "boxes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "cylinders", "boxes"], "arg_types": ["complex_spacer.ComplexSpacer", "builtins.str", {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ComplexSpacer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "complex_spacer.ComplexSpacer.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cylinders"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "boxes"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["name", "cylinders", "boxes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["name", "cylinders", "boxes"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ComplexSpacer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "complex_spacer.ComplexSpacer.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["name", "cylinders", "boxes"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ComplexSpacer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "boxes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "complex_spacer.ComplexSpacer.boxes", "name": "boxes", "type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cylinders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "complex_spacer.ComplexSpacer.cylinders", "name": "cylinders", "type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacer.get_boxes", "name": "get_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boxes of ComplexSpacer", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacer.get_cylinders", "name": "get_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cylinders of ComplexSpacer", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacer.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of ComplexSpacer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "complex_spacer.ComplexSpacer.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "complex_spacer.ComplexSpacer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "complex_spacer.ComplexSpacer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComplexSpacerLocated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "complex_spacer.ComplexSpacerLocated", "name": "ComplexSpacerLocated", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "complex_spacer", "type": "complex_spacer.ComplexSpacer"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "location", "type": "base.Point"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "reference_rotation", "type": "base.Rotation"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "reference_location", "type": "base.Point"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "complex_spacer", "mro": ["complex_spacer.ComplexSpacerLocated", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "complex_spacer.ComplexSpacerLocated.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "complex_spacer", "location", "reference_rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "complex_spacer", "location", "reference_rotation", "reference_location"], "arg_types": ["complex_spacer.ComplexSpacerLocated", "complex_spacer.ComplexSpacer", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ComplexSpacerLocated", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "complex_spacer.ComplexSpacerLocated.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "complex_spacer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "location"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reference_rotation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reference_location"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["complex_spacer", "location", "reference_rotation", "reference_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["complex_spacer", "location", "reference_rotation", "reference_location"], "arg_types": ["complex_spacer.ComplexSpacer", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ComplexSpacerLocated", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "complex_spacer.ComplexSpacerLocated.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["complex_spacer", "location", "reference_rotation", "reference_location"], "arg_types": ["complex_spacer.ComplexSpacer", "base.Point", "base.Rotation", "base.Point"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ComplexSpacerLocated", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "cam_lock_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_down", "name": "cam_lock_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_down", "name": "cam_lock_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cam_lock_left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_left", "name": "cam_lock_left", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_left of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_left", "name": "cam_lock_left", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_left of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cam_lock_right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right", "name": "cam_lock_right", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right", "name": "cam_lock_right", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cam_lock_right_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right_down", "name": "cam_lock_right_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right_down", "name": "cam_lock_right_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cam_lock_right_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right_up", "name": "cam_lock_right_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_right_up", "name": "cam_lock_right_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_right_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cam_lock_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_up", "name": "cam_lock_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.cam_lock_up", "name": "cam_lock_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["name", "location", "rotation_from_front_lock", "board_thickness"], "arg_types": ["builtins.str", "base.Point", "base.Rotation", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cam_lock_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "colides_with_board": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "board"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.colides_with_board", "name": "colides_with_board", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "board"], "arg_types": ["complex_spacer.ComplexSpacerLocated", "board.LocatedBoard"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colides_with_board of ComplexSpacerLocated", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colides_with_dowel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dowel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.colides_with_dowel", "name": "colides_with_dowel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dowel"], "arg_types": ["complex_spacer.ComplexSpacerLocated", "dowel.LocatedDowel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colides_with_dowel of ComplexSpacerLocated", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "complex_spacer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "complex_spacer.ComplexSpacerLocated.complex_spacer", "name": "complex_spacer", "type": "complex_spacer.ComplexSpacer"}}, "confirmat_7x50_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_down", "name": "confirmat_7x50_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_down", "name": "confirmat_7x50_down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_down of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "confirmat_7x50_front": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_front", "name": "confirmat_7x50_front", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_front of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_front", "name": "confirmat_7x50_front", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_front of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "confirmat_7x50_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_up", "name": "confirmat_7x50_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "complex_spacer.ComplexSpacerLocated.confirmat_7x50_up", "name": "confirmat_7x50_up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "location", "rotation"], "arg_types": ["builtins.str", "base.Point", "base.Rotation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirmat_7x50_up of ComplexSpacerLocated", "ret_type": "complex_spacer.ComplexSpacerLocated", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.get_boxes", "name": "get_boxes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacerLocated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_boxes of ComplexSpacerLocated", "ret_type": {".class": "Instance", "args": ["box.Box"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cylinders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.get_cylinders", "name": "get_cylinders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacerLocated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cylinders of ComplexSpacerLocated", "ret_type": {".class": "Instance", "args": ["cylinder.Cy<PERSON>er"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "complex_spacer.ComplexSpacerLocated.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["complex_spacer.ComplexSpacerLocated"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of ComplexSpacerLocated", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "complex_spacer.ComplexSpacerLocated.location", "name": "location", "type": "base.Point"}}, "reference_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "complex_spacer.ComplexSpacerLocated.reference_location", "name": "reference_location", "type": "base.Point"}}, "reference_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "complex_spacer.ComplexSpacerLocated.reference_rotation", "name": "reference_rotation", "type": "base.Rotation"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "complex_spacer.ComplexSpacerLocated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "complex_spacer.ComplexSpacerLocated", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Cylinder": {".class": "SymbolTableNode", "cross_ref": "cylinder.Cy<PERSON>er", "kind": "Gdef"}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "base.Dimension", "kind": "Gdef"}, "LocatedBoard": {".class": "SymbolTableNode", "cross_ref": "board.LocatedBoard", "kind": "Gdef"}, "LocatedDowel": {".class": "SymbolTableNode", "cross_ref": "dowel.LocatedDowel", "kind": "Gdef"}, "Point": {".class": "SymbolTableNode", "cross_ref": "base.Point", "kind": "Gdef"}, "Rotation": {".class": "SymbolTableNode", "cross_ref": "base.Rotation", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "complex_spacer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\src\\complex_spacer.py"}