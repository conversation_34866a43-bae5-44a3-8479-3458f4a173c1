{"data_mtime": 1761030389, "dep_lines": [2, 98, 1, 1, 1, 1, 1, 3, 66], "dep_prios": [10, 20, 5, 30, 30, 30, 10, 5, 20], "dependencies": ["os", "cad_repository", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b822ab435487a7e5cbcee1cd15e8db390a2f09d8", "id": "InitGui", "ignore_all": false, "interface_hash": "88e2b85ebc940f76e02fcfc74112822a5cba2f0c", "mtime": 1761030432, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\FreeCAD\\Mod\\CabinetPlanner\\InitGui.py", "plugin_data": null, "size": 5425, "suppressed": ["FreeCAD", "FreeCADGui", "PySide"], "version_id": "1.15.0"}