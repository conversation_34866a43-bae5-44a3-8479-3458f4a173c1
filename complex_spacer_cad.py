from typing import Optional
import FreeCAD
from FreeCAD import Vector
import Part
from abc import ABC, abstractmethod
import sys
import os

sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
from base import Dimension
from box import Box
from cylinder import Cylinder
from complex_spacer import ComplexSpacerLocated


class ComplexSpacerCad:
    def __init__(self, complex_spacer_located: ComplexSpacerLocated):
        self.__boxes = complex_spacer_located.get_boxes()
        self.__cylinders = complex_spacer_located.get_cylinders()
        self.__name = complex_spacer_located.get_name()
        self.__doc_object = None

    def get_document_object(self):
        return self.__doc_object

    def __make_cylinder(self, cylinder: Cylinder) -> Part.Shape:
        shape = Part.makeCylinder(cylinder.radius, cylinder.height)
        shape.translate(FreeCAD.Vector(cylinder.center.x, cylinder.center.y, cylinder.center.z))
        if cylinder.get_axis() == Dimension.X:
            shape.rotate(FreeCAD.Vector(cylinder.center.x, cylinder.center.y, cylinder.center.z),
                         FreeCAD.Vector(0, 1, 0), 90)
        elif cylinder.get_axis() == Dimension.Y:
            shape.rotate(FreeCAD.Vector(cylinder.center.x, cylinder.center.y, cylinder.center.z),
                         FreeCAD.Vector(-1, 0, 0), 90)
        return shape

    def __make_box(self, box: Box) -> Part.Shape:
        shape = Part.makeBox(box.x_max - box.x_min, box.y_max - box.y_min, box.z_max - box.z_min)
        shape.translate(FreeCAD.Vector(box.x_min, box.y_min, box.z_min))
        return shape

    def instantiate_in_freecad(self):
        if self.__doc_object is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part
        shape: list[Part.Shape] = []
        for cylinder in self.__cylinders:
            shape.append(self.__make_cylinder(cylinder))
        for box in self.__boxes:
            shape.append(self.__make_box(box))
        # make union of all these shapes and name them in freecad
        obj = doc.addObject("Part::Feature", self.__name)
        obj.Shape = shape[0]
        for s in shape[1:]:
            obj.Shape = obj.Shape.fuse(s)
        doc.recompute()
        obj.Label = self.__name
        self.__doc_object = obj
