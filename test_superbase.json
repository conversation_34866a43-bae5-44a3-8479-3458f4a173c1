{"$schema": "cabinet_schema.json", "variables": {}, "boards": [], "dowels": [{"name": "superbase", "length": 80, "diameter": 2, "orientation": "FRONT", "location": {"x": 0, "y": 0, "z": 0}}], "complex_spacers": [], "include": [{"name": "incln", "file_path": "test_base.json", "rotation": "NONE", "reference_location": {"x": 50, "y": 550, "z": 0}, "variables": {}}, {"name": "incle", "file_path": "test_base.json", "rotation": "ROTATE_CLOCKWISE_90", "reference_location": {"x": 550, "y": 50, "z": 20}, "variables": {}}, {"name": "incls", "file_path": "test_base.json", "rotation": "ROTATE_180", "reference_location": {"x": 50, "y": -450, "z": 20}, "variables": {}}, {"name": "inclw", "file_path": "test_base.json", "rotation": "ROTATE_COUNTER_CLOCKWISE_90", "reference_location": {"x": -450, "y": 50, "z": 20}, "variables": {}}]}